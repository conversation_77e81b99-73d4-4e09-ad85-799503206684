<template>
  <div class="chat-message-container">
    <div class="chat-message">
      <div class="avatar avatar-bot">
        <img src="@/assets/openai.svg" alt="AI Assistant" />
      </div>
      <div class="message-content">
        <!-- 加载状态 - 只在没有任何内容时显示 -->
        <div v-if="!message.done && !message.answer" class="loading-content">
          <div class="loading-text">AI正在思考中...</div>
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- 消息内容 - 有内容就显示，不管是否完成 -->
        <div v-if="message.answer || message.done" class="message-body">
            <v-md-preview :text="message.answer || ''"></v-md-preview>
            <!-- 流式输入指示器 -->
            <div v-if="message.answer && !message.done" class="streaming-indicator">
                <span class="cursor-blink">|</span>
            </div>
        </div>
        <div v-else class="message-body">
          <slot></slot>
        </div>
      </div>
    </div>
    <MessageToolbar
      v-if="message.done"
      :message="message"
      @delete="$emit('delete', $event)"
      @like="$emit('like', $event)"
    />
  </div>
</template>

<script>
import MessageToolbar from './MessageToolbar.vue'

export default {
  name: 'AssistantMessage',
  components: {
    MessageToolbar
  },
  props: {
    message: {
      type: Object,
      required: true
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-message-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: #FFFFFF;
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  padding: 15px 20px;
  margin-right: auto;
  margin-bottom: 20px;
}

.chat-message {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

.avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex-grow: 1;
}

.message-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-break: break-word;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 12px;

  .loading-text {
    color: #666;
    font-size: 14px;
  }

  .loading-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
      animation: loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

.streaming-indicator {
  display: inline-block;
  margin-left: 2px;

  .cursor-blink {
    animation: blink 1s infinite;
    font-weight: bold;
    color: #666;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
