<template>
  <div
    class="chatMain"
    v-loading="$store.state.common.chatLoading"
    element-loading-text="文件正在加载中、请稍等"
    element-loading-spinner="el-icon-loading"
  >
    <div class="cardList" ref="chatContainer">
      <div v-for="(item, index) in chatList" :key="index" style="width: 100%;">
          <UserMessage>{{ item.prompt }}</UserMessage>
          <AssistantMessage :message="item" @delete="delAns(index)" @like="handleGood({ item, index })">
            <div class="answer" ref="resizeTarget">
              <div v-if="!item.done" class="done">
                <div class="text">AI正思考中......</div>
                <PulseLoader :loading="!item.done" size="12px" />
              </div>
              <div v-else style="width: 100%">
                <!-- 正常文本回答 -->
                <template v-if="knowledgeName === '数据库对话' || knowledgeName === '计划经营'">
                  <!-- AI回答sql展示 -->
                  <div class="text" :id="'text-' + index" v-html="item.html"></div>
                  <!-- 回答表格绘制 -->
                  <div class="cust" :id="'table-' + index" v-html="item.tabledata"></div>
                  <!-- 回答图表绘制  -->
                  <PlotlyChart :ref="'Plotly-' + index" />
                </template>
                <template v-else-if="knowledgeName === '图数据库对话'">
                  <!-- AI回答sql展示 -->
                  <div class="text" :id="'text-' + index" v-html="item.html" v-show="false"></div>
                  <!-- 回答图表绘制 -->
                  <h3 :id="'h3tu_' + index" v-if="item.tabledataShow">
                    答复: 根据提供的信息, 有
                    <span v-if="item.isCountStr">
                      {{ item.tabledata.length }} 条相关内容 分别是
                      <span v-for="(data, index) in formattedTableData(item.tabledata)" :key="index">
                        {{ data.content }}
                      </span>
                    </span>
                    <span v-else> {{ item.count }} 个 </span>
                  </h3>
                  <div v-else>
                    <span v-if="item.isEmty === 1">
                      答复: 根据提供的信息暂无查询结果
                    </span>
                    <div v-else>
                      <PulseLoader :loading="true" size="12px" />
                    </div>
                  </div>
                  <GraphChart ref="GraphChart" v-show="item.SQLShow" :SQL="item.SQL" />
                </template>
                <template v-else-if="knowledgeName === '医药图谱'">
                  <!-- AI回答sql展示 -->
                  <div class="text" :id="'text-' + index" v-html="item.html"></div>
                  <!-- 回答图表绘制 -->
                  <span v-if="item.sqlContent">
                    答复: 根据提供的信息,{{ item.sqlContent }}</span>
                  <h3 :id="'h3_' + index" v-if="item.tabledataShow">
                    答复: 根据提供的信息, 有
                    <span v-if="item.isCountStr">
                      {{ !item.similarity ? item.tabledata.length : item.tabledataRoot.length }}
                      条相关内容 :
                      <span>
                        <span>
                          <span v-for="(data, index) in formattedTableData(item.tabledata)" :key="index">
                            <span v-if="!data.doc_id">
                              {{ data.content }}
                            </span>
                            <el-popover ref="myPopover" popper-class="popper-file-operation" placement="top-start" title="文件操作" width="auto" trigger="hover">
                              <div class="file-operation-item" v-if="data.authOperation == 1">
                                <el-link :underline="false" @click="handlePreview(data.doc_id, data.doc_name)" type="primary">预览</el-link>
                              </div>
                              <div class="file-operation-item" v-if="data.authOperation >= 2">
                                <el-link :underline="false" @click="handleHref(data.doc_id, data.doc_name, index)" type="primary" class="mr10">智能修订</el-link>
                                <el-link :underline="false" @click="handleDownload(data.doc_id, data.doc_name)" type="primary" class="mr10">下载</el-link>
                                <el-link :underline="false" @click="handlePreview(data.doc_id, index)" type="primary">预览</el-link>
                              </div>
                              <el-table :data="[data]" style="width: 100%" v-if="data.ishow">
                                <el-table-column prop="resultName" label="文件名" width="250" />
                                <el-table-column prop="edition" label="版本" width="80" />
                              </el-table>
                              <el-link slot="reference" v-if="data.doc_id" type="primary" target="_blank">{{ data.content }}</el-link>
                            </el-popover>
                          </span>
                        </span>
                      </span>
                    </span>
                    <span v-else> {{ item.count }} 个 </span>
                  </h3>
                  <span v-if="item.isEmty === 1">
                    答复: 根据提供的信息暂无查询结果
                  </span>
                  <GraphChart ref="medicalChart" v-show="item.SQLShow" :SQL="item.SQL" />
                </template>
                <template v-else>
                  <!-- AI回答文本展示 -->
                  <div class="text" :id="'text-' + index" v-html="item.html"></div>
                  <!-- 回答文本来源 -->
                  <div v-if="item.result && item.result.length" class="result">
                    <el-link size="mini" type="success" class="result-item" :underline="false"><i class="el-icon-view el-icon--right"></i> 根据您的需求，调用接口生成的文件:</el-link>
                    <el-link type="primary" @click="previewFile(item.result)">{{ item.result }}</el-link>
                    <el-link style="margin-left: 10px" type="primary" @click="donwloadFile(item.result)">下载</el-link>
                  </div>
                  <!-- 回来文件来源 -->
                  <div v-if="knowledgeName !== 'DeepSeek本地' && item.docs && item.docs.length" class="ask-docs">
                    <el-collapse accordion>
                      <el-collapse-item title="文件来源:">
                        <template slot="title">
                          <el-tag>文件来源</el-tag>
                        </template>
                        <div v-for="(itemDocs, k) in item.docs" :key="itemDocs.fileName + k">
                          <span style="color: #145bff">{{ k + 1 + "." }}</span>
                          <el-link type="primary" @click="previewByKKFile(itemDocs.link, itemDocs.fileName)">{{ itemDocs.fileName }}</el-link>
                        </div>
                      </el-collapse-item>
                    </el-collapse>
                  </div>
                </template>
              </div>
            </div>
          </AssistantMessage>

        <div v-if="item.helps && item.helps.length" class="tips">
          <el-tag
            class="tag-item"
            v-for="(tag, tagindex) in item.helps"
            :key="tagindex"
            @click="tipsQuestion(tag, index)"
            effect="plain"
            >{{ tag }}</el-tag
          >
        </div>
      </div>

      <WelcomeMessage v-show="!chatList.length" :subtitle="remark" />
      <CommonQuestions
        v-show="!chatList.length"
        :knowledgeName="knowledgeName"
        @getUseFaq="handleCommonQuestionClick"
      />
    </div>
    <KnowledgeSelect v-show="!this.$store.state.common.files.length" @getknowledgeValue="(val) => this.$emit('getknowledgeValue', val)" />
    <InputChat
      ref="InputChat"
      :isComplete="isComplete"
      :chatSet="chatSet"
      @getAnswer="getAnswer"
      @resetAsk="resetAsk"
      @sendStreamMessage="handleSendStreamMessage"
    />
  </div>
</template>

<script>
import { Base64 } from "js-base64";
import Tools from "./Tools.vue";
import PulseLoader from "vue-spinner/src/PulseLoader.vue";
import UserMessage from "./UserMessage.vue";
import AssistantMessage from "./AssistantMessage.vue";
import WelcomeMessage from "./WelcomeMessage.vue";
import CommonQuestions from "./CommonQuestions.vue";
import KnowledgeSelect from "./KnowledgeSelect.vue";
import { getSystemConfig } from "@/api/login";
import InputChat from "./InputChat.vue";
import serviceKnowledge from "@/api/knowledge.js";
import { getDocIdDetail } from "@/api/techdocmanage/workflow/learn/index.js";
import PlotlyChart from "./PlotlyChart.vue";
import GraphChart from "./graphChart.vue";
import { parseMarkdown, firstLetterToUpperCase, processDocs } from "../utils";
import userService from "@/api/techdocmanage/docCenter/user";
import { isSqlStatement } from "@/utils/coalmine.js";
import { markdownTable } from "markdown-table";
import {
  previewFileFn,
  donwloadFileFn,
  sendBigFileQuestion,
  sendQuestion,
} from "./commonReq.js";
import ClipboardJS from "clipboard";
import { EventSourcePolyfill } from "event-source-polyfill";
import { getToken } from "@/utils/auth";
import { preview } from "@/utils/fileStreamPreview";
export default {
  name: "",
  components: {
    PulseLoader,
    Tools,
    InputChat,
    PlotlyChart,
    GraphChart,
    WelcomeMessage,
    CommonQuestions,
    UserMessage,
    AssistantMessage,
    KnowledgeSelect,
  },
  props: {
    showAppend: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      // 问题列表
      chatList: [],
      // 提问问题
      prompt: "",
      // 等待状态
      pending: true,
      // 背景图片
      img: null,
      // 知识库名
      knowledgeName: "",
      // 知识库配置
      chatSet: null,
      // 打字机js实例
      typed: null,
      //  文件预览访问ip
      initCurrentIP: null,
      // 左侧询问label状态   常见问题 0  功能中心1   对话列表2
      askQuestionState: "2",
      // 知识库备注
      remark:
        "作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。",
      //   上次问答历史
      historyList: [
        {
          role: "user",
          content: "",
        },
        {
          role: "assistant",
          content: "",
        },
      ],
      // 采样温度
      temperature: "",
      kkFileView: "",
      // 打字机实例
      instanceTypeIt: "",
      //  流式事件源
      eventSource: null,
      // 打字完成状态
      isComplete: false,
      // 流式文件ID
      streamFId: null,
      //流式问题
      streamQuestion: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getSystemConfig();
    this.getConfigKey("previewFile").then((response) => {
      this.initCurrentIP = response.msg;
    });
    this.getConfigKey("temperature").then((response) => {
      this.temperature = response.msg;
    });
    this.getConfigKey("kkFileView").then((response) => {
      this.kkFileView = response.msg;
    });
    console.log(this.SSO.init());
  },
  mounted() {},
  methods: {
    // 初始化知识库
    initData(val) {
      this.chatSet = val;
      this.remark =
        val.remark ||
        "作为你的智能伙伴，我既能写文案、想点子，又能陪你聊天、答疑解惑。";
      this.knowledgeName = val.knowledge_name;
      this.chatList = [];
      this.typed = null;
    },
    // 清空消息
    clearMessages() {
      this.chatList = [];
      this.historyList = [
        {
          role: "user",
          content: "",
        },
        {
          role: "assistant",
          content: "",
        },
      ];
    },

    // 新的流式接口调用方法 - 从chat-v3迁移
    async startStreamChat(query) {
      try {
        // 构建请求体 - 使用chat-v3相同的格式
        const requestBody = {
          query,
          knowledge_base_name: this.knowledgeName,
          top_k: this.chatSet.top_k || 4,
          score_threshold: this.chatSet.score_threshold || 1,
          stream: true,
          model_name: this.chatSet.model_name || "deepseek-r1:14b",
          mix_type: this.chatSet.mix_type || "bm25,faiss",
          temperature: this.chatSet.temperature || 0.7,
          max_tokens: 0,
          prompt_name: this.chatSet.prompt_name || "default",
          history: this.historyList.filter(item => item.content) // 过滤空历史
        };

        // 创建AbortController用于取消请求
        this.abortController = new AbortController();

        // 使用fetch进行流式请求
        const response = await fetch('/stream-api/chat/knowledge_base_chat', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + getToken()
          },
          body: JSON.stringify(requestBody),
          signal: this.abortController.signal
        });

        if (!response.ok) {
          let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
          if (response.status === 401) {
            errorMessage = '认证失败，请重新登录';
          } else if (response.status === 403) {
            errorMessage = '权限不足，无法访问该功能';
          } else if (response.status === 404) {
            errorMessage = '服务不可用，请稍后重试';
          } else if (response.status >= 500) {
            errorMessage = '服务器内部错误，请稍后重试';
          }
          throw new Error(errorMessage);
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let buffer = '';

        const readStream = async () => {
          try {
            const { done, value } = await reader.read();

            if (done) {
              // 流式完成
              const lastMessage = this.chatList[this.chatList.length - 1];
              if (lastMessage) {
                lastMessage.done = true;
                lastMessage.isStop = true;
              }
              this.$refs.InputChat.onComplete();
              this.scrollToBottom();
              return;
            }

            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            buffer = lines.pop() || ''; // 保留最后一行（可能不完整）

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                try {
                  const jsonStr = line.slice(6).trim();

                  if (jsonStr && jsonStr !== '[DONE]') {
                    const data = JSON.parse(jsonStr);
                    const lastMessage = this.chatList[this.chatList.length - 1];

                    if (lastMessage) {
                      // 处理answer字段
                      if (data.answer) {
                        if (!lastMessage.answer) {
                          lastMessage.answer = '';
                        }
                        lastMessage.answer += data.answer;
                      }

                      // 处理docs字段
                      if (data.docs && data.docs.length > 0) {
                        lastMessage.docs = data.docs;
                      }

                      // 强制更新视图
                      this.$forceUpdate();
                      this.scrollToBottom();
                    }
                  }
                } catch (e) {
                  console.warn('解析流数据失败:', e, '原始数据:', line);
                }
              }
            }

            return readStream();
          } catch (error) {
            if (error.name !== 'AbortError') {
              console.error('流式读取错误:', error);
              const lastMessage = this.chatList[this.chatList.length - 1];
              if (lastMessage) {
                lastMessage.done = true;
                lastMessage.answer = lastMessage.answer || '抱歉，回答生成失败，请重试';
              }
              this.$refs.InputChat.onComplete();
            }
          }
        };

        // 开始读取流
        await readStream();

      } catch (error) {
        console.error('流式请求错误:', error);
        const lastMessage = this.chatList[this.chatList.length - 1];
        if (lastMessage) {
          lastMessage.done = true;
          lastMessage.answer = '网络连接失败，请检查网络状态';
        }
        this.$refs.InputChat.onComplete();
        this.$message.error(error.message || '请求失败');
      }
    },

    // 处理流式消息发送 - 按照最佳实践实现
    async handleSendStreamMessage({ query, chatSettings, history }) {
      try {
        console.log('开始流式聊天:', { query, chatSettings });

        // 1. 添加用户消息
        this.chatList.push({
          prompt: query,
          done: true, // 用户消息立即完成
          answer: '',
          isStop: true,
          id: Date.now(),
          helps: [],
          docs: [],
          isRecords: false,
          isVoicePlay: false,
        });

        // 2. 添加AI消息占位符
        this.chatList.push({
          prompt: '', // AI消息没有prompt
          done: false, // AI消息开始时未完成
          answer: '', // 流式内容会填充到这里
          isStop: true,
          id: Date.now() + 1,
          helps: [],
          docs: [],
          isRecords: false,
          isVoicePlay: false,
        });

        // 3. 滚动到底部
        this.scrollToBottom();

        // 4. 开始流式聊天
        await this.startStreamChat(query);

        // 5. 更新历史记录
        this.historyList = [
          {
            role: "user",
            content: query,
          },
          {
            role: "assistant",
            content: this.chatList[this.chatList.length - 1].answer || "",
          },
        ];

      } catch (error) {
        console.error('处理流式消息失败:', error);
        this.$message.error('发送消息失败');
        this.$refs.InputChat.onComplete();
      }
    },


    // 获取图片配置
    getSystemConfig() {
      let that = this;
      getSystemConfig().then((res) => {
        that.$nextTick(() => {
          new Promise(function (resolve, reject) {
            var ImgObj = new Image(); //判断图片是否存在
            ImgObj.src = res.data.logoPic;
            ImgObj.onload = function (_res) {
              that.img = res.data.logoPic;
            };
            ImgObj.onerror = function (err) {};
          }).catch((e) => {});
        });
      });
    },
    // 文件预览
    previewFile(path) {
      previewFileFn(path, this.initCurrentIP, this.$store.state.user.userId);
    },
    //kkfile 预览
    previewByKKFile(path, name) {
      preview(path, name, "", this.kkFileView);
    },
    // 文件下载
    donwloadFile(path) {
      donwloadFileFn(path, this.$store.state.user.userId);
    },
    // AI提示列表提问
    tipsQuestion(val, index) {
      this.chatList[index].helps = [];
      this.$refs.InputChat.askQuestion(val, this.chatSet);
    },
    // 处理来自 CommonQuestions 组件的点击事件
    handleCommonQuestionClick(question) {
      this.$refs.InputChat.askQuestion(question, this.chatSet);
    },
    // 提问
    async askQuestion(val, flag) {
      // this.$refs.InputChat.askQuestion(prompt, this.chatSet);
      this.askQuestionState = flag;
      this.clearRecords();
      this.$refs.InputChat.onComplete();
      if (this.chatList.length && !this.chatList.at(-1).done) {
        return this.$message.info("请等待上一个问题回答完成");
      }
      if (this.typed || this.instanceTypeIt) {
        if (this.chatList.length) {
          this.stop(this.chatList.length - 1);
        }
      } else {
        this.chatList = [];
      }

      switch (flag) {
        // 常见问题回调
        case "0":
          this.$refs.InputChat.askQuestion(val, this.chatSet);
          break;
        // 功能中心回调
        case "1":
          const { model_name, prompt_name, score_threshold, top_k, label } =
            val;
          //  特殊处理 label为文档概述的情况
          if (label === "文档概述") {
            this.loadAsk({ done: false, prompt: "文档概述" });
            // 大文件处理
            let filename = this.$store.state.common.files.join(",");
            // let filename = "QC-SMP-003 样品流程管理规程.docx";
            const { code, data } = await sendBigFileQuestion(
              this.knowledgeName,
              filename,
              model_name,
              this.temperature
            );
            if (code === 200) {
              this.textAnswer({
                id: false,
                result: [],
                docs: [],
                answer: data,
                done: true,
                isStop: true,
              });
            }
          } else {
            this.$refs.InputChat.askQuestion(val.query, {
              ...this.chatSet,
              model_name,
              prompt_name,
              score_threshold,
              top_k,
              label,
            });
          }
          break;
        // 对话列表回调
        case "2":
          const { id, query, done, isStop, answer } = val;
          this.chatList.push({ done, isStop, prompt: query, id, answer });
          this.isHistory = true;
          this.getAnswer(val);
          break;
        default:
          break;
      }
    },
    // 问题加载
    loadAsk(val) {
      const { done, prompt } = val;
      this.chatList.push({
        done,
        prompt,
        isStop: true,
        id: true,
        helps: [],
        isRecords: false,
        isVoicePlay: false,
      });
      this.scrollToBottom();
      this.isHistory = false;
    },
    // 重置回答
    resetAsk() {
      this.chatList = [];
    },
    // 获取AI答案回调
    getAnswer(val) {
      this.scrollToBottom();
      const istext = [
        "数据库对话",
        "图数据库对话",
        "医药图谱",
        "医药法规",
        "计划经营",
        "管理规程",
      ].includes(this.knowledgeName);
      if (istext) {
        switch (this.knowledgeName) {
          case "数据库对话":
            // 调用数据库问答方法
            this.databaseAnswer(val);
            break;
          case "图数据库对话":
            // 调用数据库问答方法
            this.graphAnswer(val);
            break;
          case "医药图谱":
            // 调用医药图谱数据库问答方法
            this.medicalAtlasAnswer(val);
            break;
          case "医药法规":
            // 调用医药法规数据库问答方法
            this.medicalRegulationsAnswer(val);
            break;
          case "计划经营":
            // 调用计划经营数据库问答方法
            this.plannedOperationAnswer(val);
            break;
          case "管理规程":
            // 调用管理规程问答方法
            this.managementProceduresAnswer(val);
            break;
          default:
            break;
        }
      } else {
        // 纯文本答案
        this.textAnswer(val);
      }
    },
    // 调用管理规程问答方法
    managementProceduresAnswer(val) {
      let { message, query, answer, action } = val;
      if (action == "0") {
        this.streamFId = null;
      }
      if (this.isHistory) {
        this.chatList.at(-1).html = answer;
      } else {
        if (answer) {
          this.textAnswer(val);
        } else {
          if (message) {
            this.chatList.at(-1).done = true;
            this.chatList.at(-1).isStop = true;
            message = parseMarkdown(message);
            message = message.replace(/[\n\t\r\f\v]+/g, "");
            this.$nextTick(() => {
              const id = "#text-" + JSON.stringify(this.chatList.length - 1);
              this.typed = new Typed(id, {
                strings: [message], // 使用处理后的答案
                // typeSpeed: 15,
                typeSpeed: 1,
                loop: false,
                showCursor: false,
                onComplete: () => {
                  console.log("输入完成");
                  this.$forceUpdate();
                  this.scrollToBottom();
                  this.$refs.InputChat.onComplete();
                  // 刷新左侧历史记录（如果条件满足）
                  // if (this.askQuestionState === "2") {
                  //   this.refreshLeftList();
                  // }
                },
              });
            });
          } else if (query) {
            this.streamQuestion = query;
            const url =
              process.env.VUE_APP_BASE_API +
              // `/techDocManage/answerResultStream/stream/?templateName=${templateName}`;
              `/techDocManage/answerResultStream/stream/?query=${query}&knowledgeName=${this.knowledgeName}&fid=${this.streamFId}&promptName=${this.chatSet.prompt_name}&temperature=${this.chatSet.temperature}`;
            let msg = "";
            let allMsg = [];
            if (typeof EventSource !== "undefined") {
              console.warn("发送流式请求");
              this.eventSource = new EventSourcePolyfill(url, {
                headers: {
                  Authorization: "Bearer " + getToken(),
                  // 设置重连时间
                  heartbeatTimeout: 60 * 60 * 1000,
                },
              });
              this.eventSource.onmessage = (event) => {
                // allMsg = JSON.parse(event.data).keyWord;
                allMsg.push(JSON.parse(event.data).keyWord);
                this.chatList.at(-1).done = true;
                this.chatList.at(-1).isStop = true;
                this.chatList.at(-1).isComplete = false;
                this.chatList.at(-1).isRecords = true;
                if (!this.instanceTypeIt) {
                  this.$nextTick(() => {
                    this.instanceTypeIt = this.createTypeItInstance();
                  });
                }
                msg = JSON.parse(event.data).keyWord;
                this.streamFId = JSON.parse(event.data).id;
                this.chatList.at(-1).fileName = JSON.parse(event.data).type;
                // 打字操作
                this.typeItAfterComplete(msg);
              };
              this.eventSource.onerror = (error) => {
                this.eventSource.close();
                console.log(error);

                if (error.target.readyState == 2) {
                  this.isComplete = true;
                  this.typeItAfterComplete();
                }
                // 追加文本
                this.chatList.at(-1).appentText = allMsg;
                const errMsg =
                  "No activity within 45000 milliseconds. 4070 chars received. Reconnecting.";
                // if (error.error ==errMsg||error.target.readyState==2) {
                if (error.error == errMsg) {
                  console.log("超时报错");
                  this.eventSource.close();
                  this.scrollToBottom();
                  ` `;
                  this.hideCursor();
                  this.chatList.at(-1).isComplete = true;
                  this.$refs.toolsRef[this.chatList.length - 1].update(
                    this.chatList
                  );
                  this.$forceUpdate();
                  this.instanceTypeIt = null;
                  this.$refs.InputChat.onComplete();
                  this.isComplete = false;
                }
              };
            } else {
              console.log("Your browser does not support Server-Sent Events.");
            }
          }
        }
      }
    },

    // 打字完成后操作
    typeItAfterComplete(msg = "") {
      this.$nextTick(() => {
        console.log(this.instanceTypeIt);
        this.instanceTypeIt
          .type(msg)
          .go()
          .flush(async () => {
            console.log(this.isComplete, "打字完成");
            if (this.isComplete) {
              this.hideCursor();
              this.chatList.at(-1).isComplete = true;
              this.$refs.toolsRef[this.chatList.length - 1].update(
                this.chatList
              );
              this.$forceUpdate();
              this.instanceTypeIt = null;
              this.$refs.InputChat.onComplete();
              this.isComplete = false;
              this.scrollToBottom();
              this.askQuestionState === "2" && this.refreshLeftList();
              // await serviceKnowledge.addEventSourceHistory({
              //   query,
              //   result: allMsg,
              //   knowledge_name: this.knowledgeName,
              //   generate: "0",
              // });
            }
          });
      });
    },
    // 隐藏光标
    hideCursor() {
      const selector = `#text-${this.chatList.length - 1} .ti-cursor`;
      const element = this.$el.querySelector(selector);
      element && (element.style.display = "none");
    },
    // 创建TypeIt实例
    createTypeItInstance() {
      const id = "#text-" + JSON.stringify(this.chatList.length - 1);
      return new TypeIt(id, {
        speed: 15,
        html: true,
        lifeLike: false,
        cursor: true,
        // cursorChar:'$$$',
        afterComplete: function (instance) {
          // console.log(instance, "完成");
        },
      });
    },
    // 执行图数据库方法
    executeCypher(query) {
      if (!query) return;
      let neo4j = require("neo4j-driver");
      // 创建实例
      this.driver = neo4j.driver(
        "bolt://192.168.0.150:7688",
        neo4j.auth.basic("neo4j", "a1b2c3d4")
      );
      console.log(
        "🚀 ~ file: AuthorArticleSearch.vue ~ line 46 ~ mounted ~  this.drive",
        this.driver
      );

      let me = this;
      me.records = [];
      let session = this.driver.session();
      session
        .run(query, {})
        .then(async (result) => {
          me.records = result.records;
          console.log("neo4j 查询结果", result.records);
          // if (!result.records.length) {
          //   console.log(1111);
          //   me.$refs.InputChat.onComplete();
          //   me.chatList.at(-1).tabledataShow = false;
          //   me.chatList.at(-1).isEmty = 1;
          // }

          if (me.knowledgeName === "图数据库对话") {
            if (result.records && result.records.length) {
              let countStr = result.records[0].keys.at(-1).toLowerCase();
              if (countStr.startsWith("count")) {
                me.chatList.at(-1).SQLShow = false;
                me.chatList.at(-1).isCountStr = false;
                me.chatList.at(-1).tabledataShow = true;
                let key = result.records[0].keys.at(-1);
                let value = result.records[0]?._fields[0]?.low;
                me.chatList.at(-1).count = value;
              } else {
                const tableColumns = result.records[0].keys.map((v) => ({
                  prop: v,
                  label: v,
                }));
                const tabledata = result.records.map((v, index) => {
                  let tabObj = {};
                  tableColumns.map((i, tabIndx) => {
                    return (tabObj[i.prop] = JSON.stringify(
                      v._fields[tabIndx]
                    ));
                  });
                  return tabObj;
                });

                const listObj = tabledata.map((v) => {
                  let i = result.records[0].keys.at(-1);
                  return JSON.parse(v[i])?.properties?.ELEMENT_NAME;
                });
                console.log(listObj);
                console.log(me.chatList.at(-1));
                // 展示表格和图
                me.chatList.at(-1).isCountStr = true;
                me.chatList.at(-1).SQLShow = true;
                me.chatList.at(-1).tabledataShow = true;
                // me.chatList.at(-1).tabledata = tabledata;
                // me.chatList.at(-1).tableColumns = tableColumns;
                me.chatList.at(-1).tabledata = listObj;
                me.chatList.at(-1).count = listObj.length;
                me.$refs.GraphChart.at(-1).draw(query);
                me.$nextTick(async () => {
                  const h3Element = document.getElementById(
                    "h3tu_" + JSON.stringify(this.chatList.length - 1)
                  );
                  const { code } = await serviceKnowledge.updateKnowledgeChat({
                    id: this.chatList.at(-1).id,
                    format_result: me.getTextFromElement(h3Element),
                  });
                  if (code === 200) {
                    // 刷新左侧历史列表
                    this.$refs.InputChat.onComplete();
                  }
                });
                this.$forceUpdate();
              }
            } else {
              console.log("clear loading");

              me.chatList.at(-1).tabledataShow = false;
              me.chatList.at(-1).isEmty = 1;
              // 加载完成
              me.chatList.at(-1).done = true;
              me.$refs.InputChat.onComplete();
            }
            session.close();
            // 加载完成
            me.chatList.at(-1).done = true;
            this.$forceUpdate();
          } else if (
            me.knowledgeName === "医药图谱" ||
            me.knowledgeName === "医药法规"
          ) {
            if (result.records && result.records.length) {
              let properties = [];
              result.records.forEach((v) => {
                v._fields.forEach((i) => {
                  properties.push(i.properties);
                });
              });
              properties = properties.filter(
                (obj) => Object.keys(obj).length > 0
              );
              const arrDistinctByProp = (arr, prop) => {
                let obj = {};
                return arr.reduce(function (preValue, item) {
                  obj[item[prop]]
                    ? ""
                    : (obj[item[prop]] = true && preValue.push(item));
                  return preValue;
                }, []);
              };
              let root = [];
              let reslut = [];
              properties.forEach((v) => {
                if (v?.doc_id) {
                  reslut.push({
                    ...v,
                  });
                  reslut = arrDistinctByProp(reslut, "doc_id");
                } else {
                  let obj = {};
                  Object.keys(v).map((i) => {
                    obj[i] = v[i];
                  });
                  root.push(obj);
                }
              });
              if (root.length) {
                root = root.map((v) => {
                  let name = v.term_no ? "---" + v.term_no : "";
                  return {
                    doc_name: v.low_name + name,
                    doc_id: "",
                  };
                });
              }
              if (!reslut.length) {
                reslut = [...root];
              }

              // 截取语句中where之后的字符串  并截取字符串中被单引号包裹的字段
              const whereIndex = query.toLowerCase().indexOf("where");
              if (whereIndex !== -1) {
                let partAfterWhere = query
                  .substring(whereIndex + "where".length)
                  .trim(); // 去除前后的空格
                // 使用正则表达式匹配所有单引号内的内容
                let regex = /'(.*?)'/g; // 非贪婪匹配单引号内的内容
                let matches = partAfterWhere.match(regex);

                // 输出匹配结果
                if (matches) {
                  matches = matches
                    .map((item) => item.replace(/'/g, ""))
                    .join("---");
                  const stringSimilarity = require("string-similarity");
                  // let similarity = stringSimilarity.compareTwoStrings(
                  //   matches,
                  //   root[0].doc_name
                  // );
                  // if (similarity < 0.5) {
                  //   // 展示 tabledataRoot 数据
                  //   me.chatList.at(-1).similarity = true;
                  // } else {
                  //   // 展示 tabledata 数组数据
                  //   me.chatList.at(-1).similarity = false;
                  // }
                } else {
                  console.log(
                    'No matches found within the single quotes after "where".'
                  );
                }
              }

              // 展示表格和图
              me.chatList.at(-1).SQLShow = true;
              me.chatList.at(-1).tabledataShow = true;

              // me.chatList.at(-1).tabledata = tabledata;
              // me.chatList.at(-1).tableColumns = tableColumns;
              // 查询  文件是否有智能核稿的权限
              let reqDocIds = reslut
                .map((item) => {
                  if (item.doc_id) {
                    return item.doc_id;
                  }
                })
                .filter((item) => {
                  return item;
                });
              if (reqDocIds.length) {
                // 发送请求获取文件列表
                let fileAuthData = await serviceKnowledge.getFileAuth({
                  ids: reqDocIds.join(","),
                });
                reslut = reslut.map((item) => {
                  let file = fileAuthData.data.find((item1) => {
                    return item1.docId === item.doc_id;
                  });
                  console.log(file);
                  return {
                    ...item,
                    resultName: file?.resultName,
                    edition: file?.edition,
                    authOperation: file?.authOperation * 1 || 1,
                    ishow: file ? true : false,
                  };
                });
              }

              console.log(reslut);

              me.chatList.at(-1).tabledata = reslut;
              me.chatList.at(-1).tabledataRoot = root;
              this.$forceUpdate();
              me.$refs.medicalChart.at(-1).draw(query);
              me.$nextTick(async () => {
                // const h3Element = document.getElementById(
                //   "h3_" + JSON.stringify(this.chatList.length - 1)
                // );

                // const { code } = await serviceKnowledge.updateKnowledgeChat({
                //   id: me.chatList.at(-1).id,
                //   format_result: me.getTextFromElement(h3Element),
                // });
                // if (code === 200) {
                //   // 刷新左侧历史列表
                me.$refs.InputChat.onComplete();
                //   // 刷新左侧历史记录
                //   me.askQuestionState === "2" && this.refreshLeftList();
                // }
              });
            } else {
              me.chatList.at(-1).tabledataShow = false;
              me.chatList.at(-1).isEmty = 1;
              // 加载完成
              me.chatList.at(-1).done = true;
              me.$refs.InputChat.onComplete();
            }
            session.close();
            // 加载完成
            me.chatList.at(-1).done = true;
            me.$forceUpdate();
          }
        })
        .catch(function (error) {
          console.log("Cypher 执行失败！", error);
          if (me.knowledgeName === "图数据库对话") {
            me.chatList.at(-1).isEmty = 1;
            me.chatList.at(-1).tabledataShow = false;
            me.$refs.InputChat.onComplete();
            me.chatList.at(-1).done = true;
            me.$forceUpdate();
            me.driver.close();
          } else {
            me.$message.error("请重新提问");
            me.chatList.at(-1).tabledataShow = false;
            me.$refs.InputChat.onComplete();
            me.resetAsk();
            me.driver.close();
          }
        });
    },
    // 调用计划经营数据库问答方法
    plannedOperationAnswer(val) {
      let {
        id,
        answer,
        done,
        isStop,
        isflag,
        should_generate_chart,
        fig,
        df,
        isSingleRow,
        tableStr,
        isSql,
      } = val;
      this.chatList.at(-1).id = id;
      this.chatList.at(-1).isflag = isflag;
      this.chatList.at(-1).should_generate_chart = should_generate_chart;
      this.chatList.at(-1).isStop = isStop;
      this.chatList.at(-1).fig = fig;
      this.chatList.at(-1).df = df;
      this.chatList.at(-1).good = false;
      this.chatList.at(-1).done = done;
      console.log(answer, "done");
      // 绘图方法
      const drawChart = (config) => {
        this.$nextTick(() => {
          this.$refs[
            "Plotly-" + JSON.stringify(this.chatList.length - 1)
          ][0].draw({
            id: "myPlot-" + JSON.stringify(this.chatList.length - 1),
            isShow: true,
            data: config.data,
            layout: config.layout,
          });
        });
      };

      this.$nextTick(() => {
        if (this.isHistory) {
          try {
            let parsedData = JSON.parse(tableStr);
            const contentForDisplay = Array.isArray(parsedData)
              ? markdownTable(parsedData)
              : parsedData;
            this.chatList.at(-1).html = parseMarkdown(contentForDisplay);
            // if (parsedData.draw.should_generate_chart) {
            //   drawChart(parsedData.draw.fig);
            // }
          } catch (error) {
            console.error("Error parsing JSON:", error);
          }
          this.$forceUpdate();
        } else {
          if (isflag && isSql) {
            const id = "#table-" + JSON.stringify(this.chatList.length - 1);
            if (isSingleRow) {
              df = isSingleRow;
            }
            // 追加文本
            this.chatList.at(-1).appentText = df;
            this.typed = new Typed(id, {
              strings: [df], //输出的文字
              typeSpeed: 15, //打字的速度
              loop: false,
              showCursor: false,
              onComplete: async () => {
                console.log("表格绘制完成");
                this.$refs.InputChat.onComplete();
                // 刷新左侧历史记录
                this.askQuestionState === "2" && this.refreshLeftList();
                if (should_generate_chart) {
                  drawChart(fig);
                }
              },
            });
          }
          if (!isSql) {
            const id = "#text-" + JSON.stringify(this.chatList.length - 1);
            this.typed = new Typed(id, {
              strings: [answer], //输出的文字
              typeSpeed: 15, //打字的速度
              loop: false,
              showCursor: false,
              onComplete: async () => {
                this.$refs.InputChat.onComplete();
                // 刷新左侧历史记录
                this.askQuestionState === "2" && this.refreshLeftList();
              },
            });
          }

          // 以下注释为显示sql版本
          // const id = "#text-" + JSON.stringify(this.chatList.length - 1);
          // console.log(id, answer);
          // console.log(this.knowledgeName, "knowledgeName");
          // this.typed = new Typed(id, {
          //   strings: [answer], //输出的文字
          //   typeSpeed: 15, //打字的速度
          //   loop: false,
          //   showCursor: false,
          //   onComplete: () => {
          //     console.log("输入完成");
          //     this.$refs.InputChat.onComplete();
          //     if (isflag) {
          //       const id = "#table-" + JSON.stringify(this.chatList.length - 1);
          //       console.log(id, df);
          //       this.typed = new Typed(id, {
          //         strings: [df], //输出的文字
          //         typeSpeed: 15, //打字的速度
          //         loop: false,
          //         showCursor: false,
          //         onComplete: () => {
          //           console.log("表格绘制完成");
          //           if (should_generate_chart) {
          //             this.$nextTick(() => {
          //               this.$refs[
          //                 "Plotly-" + JSON.stringify(this.chatList.length - 1)
          //               ][0].draw({
          //                 id:
          //                   "myPlot-" +
          //                   JSON.stringify(this.chatList.length - 1),
          //                 isShow: true,
          //                 data: fig.data,
          //                 layout: fig.layout,
          //               });
          //             });
          //           }
          //         },
          //       });
          //     }
          //   },
          // });
        }
      });
    },
    // 调用医药法规数据库问答方法
    medicalRegulationsAnswer(val) {
      const { id, answer, done, docs, isStop, helps, prompt } = val;
      // 获取 chatList 的最后一个元素并更新其属性
      const lastChat = this.chatList.at(-1);
      lastChat.id = id;
      lastChat.done = done; // 初始设置为传入的 done 值
      lastChat.docs = docs;
      lastChat.isStop = isStop;
      // 重置与 SQL 和表格数据相关的属性
      lastChat.SQL = "";
      lastChat.tabledataRoot = "";
      lastChat.tabledata = "";
      lastChat.SQLShow = false;
      lastChat.tabledataShow = false;
      lastChat.good = false;
      lastChat.similarity = false;
      lastChat.isCountStr = true;

      // 强制 Vue 更新视图
      this.$forceUpdate();

      // 定义一个标志位，用于标记是否需要特殊处理字符串
      let isStr = false;

      this.$nextTick(async () => {
        // 根据是否在历史记录中，执行不同的逻辑
        if (this.isHistory) {
          let newStr = "";
          let initstr = answer.toLowerCase();

          // 检查 answer 是否包含 SQL、Cypher 或 Match 语句，并提取实际查询字符串
          if (
            initstr.includes("select") ||
            initstr.includes("cypher") ||
            initstr.includes("match")
          ) {
            if (initstr.includes("select")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=select)/i, "");
            } else if (initstr.includes("match")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=match)/i, "");
            } else {
              isStr = true; // 如果不包含这些关键字，则直接处理原始字符串
            }
          } else {
            isStr = true;
          }
          // 删除 <p> 和 </p> 标签
          if (newStr.includes("<p>") || newStr.includes("</p>")) {
            newStr = newStr.replace(/<\/?p>/g, "");
          }

          // 如果 isStr 为 true，则直接处理原始字符串
          if (isStr) {
            await this.$nextTick(() => {
              lastChat.done = true;
              lastChat.html = answer;
              this.$forceUpdate();
            });
          }
          // else {
          //   // 处理 SQL 查询
          //   if (newStr.toLowerCase().startsWith("select")) {
          //     const { code, msg } = await serviceKnowledge.graphdataBaseSQl({
          //       sql: newStr,
          //     });
          //     if (code === 200 && msg !== "查询结果为空!") {
          //       await this.$nextTick(() => {
          //         lastChat.done = true;
          //         let str = parseMarkdown("答复: 根据提供的信息," + msg);
          //         // newStr += "\n" + str;
          //         // lastChat.html = newStr;
          //         lastChat.html = str;
          //         this.$forceUpdate();
          //       });
          //     }
          //   }
          // }
        } else {
          // 处理非历史记录的情况
          let newStr = "";
          let initstr = answer.toLowerCase();

          // 提取查询字符串的逻辑同上
          if (
            initstr.includes("select") ||
            initstr.includes("cypher") ||
            initstr.includes("match")
          ) {
            if (initstr.includes("select")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=select)/i, "");
            } else if (initstr.includes("match")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=match)/i, "");
            } else {
              isStr = true;
            }
          } else {
            isStr = true;
          }
          // 删除 <p> 和 </p> 标签
          if (newStr.includes("<p>") || newStr.includes("</p>")) {
            newStr = newStr.replace(/<\/?p>/g, "");
          }
          if (isStr) {
            const { code, data } = await sendQuestion(
              prompt,
              this.chatSet,
              this.historyList
            );
            if (code === 200 && data.answer) {
              let { answer, docs, id, result, router, helps } = data;
              // 1.保存问答上次历史
              this.historyList = [
                {
                  role: "user",
                  content: prompt,
                },
                {
                  role: "assistant",
                  content: answer,
                },
              ];
              // 2. 过滤文档来源数据
              if (docs && docs.length) {
                docs = processDocs(docs);
              }
              // 3.生成文件地址
              result = result
                ? process.env.VUE_APP_BASE_GATE_WAY_URL + "/" + result
                : "";
              // 处理answer 是否含有  ’未找到相关内容，无法回答您的问题‘ 字符或 存在则将answer 重置为  未找到相关内容，无法回答您的问题 字符
              if (answer.includes("未找到相关内容，无法回答您的问题")) {
                answer = "未找到相关内容，无法回答您的问题";
              }
              console.log(answer);
              console.log(parseMarkdown(answer));
              // 4.回答答案回调 并清空问题 刷新左侧历史数据
              this.textAnswer({
                id,
                answer: parseMarkdown(answer),
                done: true,
                docs,
                result,
                isStop: true,
                helps,
              });
              // 6.如果 router存在  则跳转对应功能
              if (router) {
                let routerUrl = firstLetterToUpperCase(router);
                //   缩小当前弹框
                this.$store.commit("common/isMinimize", true);
                this.$router.push({
                  name: routerUrl,
                });
              }
            } else {
              this.resetAsk();
              this.pending = true;
              return this.$message.error("请重新提问");
            }
          } else {
            // 处理 SQL、Cypher 查询的逻辑同上
            if (newStr.toLowerCase().startsWith("select")) {
              const { code, msg } = await serviceKnowledge.graphdataBaseSQl({
                sql: newStr,
              });
              if (code === 200 && msg !== "查询结果为空!") {
                lastChat.done = true;
                await this.$nextTick(() => {
                  let str = parseMarkdown(
                    "答复: 经查法规文件,回答内容如下:" + "\n" + msg
                  );
                  // 追加文本
                  lastChat.appentText = str;
                  const ids = `#text-${this.chatList.length - 1}`;
                  this.typed = new Typed(ids, {
                    // 带查询语句
                    // strings: [newStr],
                    // 不带查询语句
                    strings: [str],
                    typeSpeed: 15,
                    loop: false,
                    showCursor: false,
                    onComplete: async () => {
                      this.pending = true;
                      const { code: updateCode } =
                        await serviceKnowledge.updateKnowledgeChat({
                          id,
                          format_result: str,
                        });
                      if (updateCode === 200) {
                        this.$refs.InputChat.onComplete();
                        if (this.askQuestionState === "2") {
                          this.refreshLeftList();
                        }
                      }
                      lastChat.helps = helps;
                      this.$forceUpdate();
                      this.scrollToBottom();
                    },
                  });
                });
              } else {
                console.log(this.chatSet);
                const { code, data } = await sendQuestion(
                  prompt,
                  this.chatSet,
                  this.historyList
                );
                if (code === 200 && data.answer) {
                  let { answer, docs, id, result, router, helps } = data;
                  // 1.保存问答上次历史
                  this.historyList = [
                    {
                      role: "user",
                      content: prompt,
                    },
                    {
                      role: "assistant",
                      content: answer,
                    },
                  ];
                  // 2. 过滤文档来源数据
                  if (docs && docs.length) {
                    docs = processDocs(docs);
                  }
                  // 3.生成文件地址
                  result = result
                    ? process.env.VUE_APP_BASE_GATE_WAY_URL + "/" + result
                    : "";
                  // 处理answer 是否含有  ’未找到相关内容，无法回答您的问题‘ 字符或 存在则将answer 重置为  未找到相关内容，无法回答您的问题 字符
                  if (answer.includes("未找到相关内容，无法回答您的问题")) {
                    answer = "未找到相关内容，无法回答您的问题";
                  }

                  // 4.回答答案回调 并清空问题 刷新左侧历史数据
                  this.textAnswer({
                    id,
                    answer: parseMarkdown(answer),
                    done: true,
                    docs,
                    result,
                    isStop: true,
                    helps,
                  });
                  // 6.如果 router存在  则跳转对应功能
                  if (router) {
                    let routerUrl = firstLetterToUpperCase(router);
                    //   缩小当前弹框
                    this.$store.commit("common/isMinimize", true);
                    this.$router.push({
                      name: routerUrl,
                    });
                  }
                } else {
                  this.resetAsk();
                  this.pending = true;
                  return this.$message.error("请重新提问");
                }
              }
            } else {
              const { code, data } = await sendQuestion(
                prompt,
                this.chatSet,
                this.historyList
              );
              if (code === 200 && data.answer) {
                let { answer, docs, id, result, router, helps } = data;
                // 1.保存问答上次历史
                this.historyList = [
                  {
                    role: "user",
                    content: prompt,
                  },
                  {
                    role: "assistant",
                    content: answer,
                  },
                ];
                // 2. 过滤文档来源数据
                if (docs && docs.length) {
                  docs = processDocs(docs);
                }
                // 3.生成文件地址
                result = result
                  ? process.env.VUE_APP_BASE_GATE_WAY_URL + "/" + result
                  : "";
                // 4.回答答案回调 并清空问题 刷新左侧历史数据
                this.textAnswer({
                  id,
                  answer: parseMarkdown(answer),
                  done: true,
                  docs,
                  result,
                  isStop: true,
                  helps,
                });
                // 6.如果 router存在  则跳转对应功能
                if (router) {
                  let routerUrl = firstLetterToUpperCase(router);
                  //   缩小当前弹框
                  this.$store.commit("common/isMinimize", true);
                  this.$router.push({
                    name: routerUrl,
                  });
                }
              } else {
                this.resetAsk();
                this.pending = true;
                return this.$message.error("请重新提问");
              }
            }
          }
        }
      });
    },

    // 调用医药图谱数据库问答方法
    medicalAtlasAnswer(val) {
      // 从传入的对象中解构出所需的属性
      const { id, answer, done, docs, isStop, helps } = val;

      // 获取 chatList 的最后一个元素并更新其属性
      const lastChat = this.chatList.at(-1);
      lastChat.id = id;
      lastChat.done = done; // 初始设置为传入的 done 值
      lastChat.docs = docs;
      lastChat.isStop = isStop;
      // 重置与 SQL 和表格数据相关的属性
      lastChat.SQL = "";
      lastChat.tabledataRoot = "";
      lastChat.tabledata = "";
      lastChat.SQLShow = false;
      lastChat.tabledataShow = false;
      lastChat.good = false;
      lastChat.similarity = false;
      lastChat.isCountStr = true;

      // 强制 Vue 更新视图
      this.$forceUpdate();

      // 定义一个标志位，用于标记是否需要特殊处理字符串
      let isStr = false;

      this.$nextTick(async () => {
        // 根据是否在历史记录中，执行不同的逻辑
        if (this.isHistory) {
          let newStr = "";
          let initstr = answer.toLowerCase();

          // 检查 answer 是否包含 SQL、Cypher 或 Match 语句，并提取实际查询字符串
          if (
            initstr.includes("select") ||
            initstr.includes("cypher") ||
            initstr.includes("match")
          ) {
            if (initstr.includes("select")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=select)/i, "");
            } else if (initstr.includes("match")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=match)/i, "");
            } else {
              isStr = true; // 如果不包含这些关键字，则直接处理原始字符串
            }
          } else {
            isStr = true;
          }
          // 删除 <p> 和 </p> 标签
          if (newStr.includes("<p>") || newStr.includes("</p>")) {
            newStr = newStr.replace(/<\/?p>/g, "");
          }

          // 如果 isStr 为 true，则直接处理原始字符串
          if (isStr) {
            await this.$nextTick(() => {
              lastChat.done = true;
              lastChat.html = answer;
              this.$forceUpdate();
            });
          } else {
            // 处理 SQL 查询
            if (newStr.toLowerCase().startsWith("select")) {
              const { code, msg } = await serviceKnowledge.graphdataBaseSQl({
                sql: newStr,
              });

              if (code === 200) {
                await this.$nextTick(() => {
                  lastChat.done = true;
                  let str = parseMarkdown("答复: 根据提供的信息," + msg);
                  // newStr += "\n" + str;
                  // lastChat.html = newStr;
                  lastChat.html = str;
                  this.$forceUpdate();
                });
              }
            } else {
              console.log(newStr);
              // 处理 Cypher 查询或普通文本
              await this.$nextTick(() => {
                lastChat.done = true;
                this.executeCypher(newStr);
                // lastChat.html = newStr;
                // lastChat.helps = helps;
                this.$forceUpdate();
                this.scrollToBottom();
              });
            }
          }
        } else {
          // 处理非历史记录的情况
          let newStr = "";
          let initstr = answer.toLowerCase();

          // 提取查询字符串的逻辑同上
          if (
            initstr.includes("select") ||
            initstr.includes("cypher") ||
            initstr.includes("match")
          ) {
            if (initstr.includes("select")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=select)/i, "");
            } else if (initstr.includes("match")) {
              newStr = answer
                .replace(/^```|```$/g, "")
                .replace(/^[\s\S]*?(?=match)/i, "");
            } else {
              isStr = true;
            }
          } else {
            isStr = true;
          }
          // 删除 <p> 和 </p> 标签
          if (newStr.includes("<p>") || newStr.includes("</p>")) {
            newStr = newStr.replace(/<\/?p>/g, "");
          }
          if (isStr) {
            // 追加文本
            lastChat.appentText = answer;
            // 使用 Typed.js 逐字显示答案
            await this.$nextTick(() => {
              lastChat.done = true;
              const ids = `#text-${this.chatList.length - 1}`;
              this.typed = new Typed(ids, {
                strings: [answer],
                typeSpeed: 15,
                loop: false,
                showCursor: false,
                onComplete: async () => {
                  this.pending = true;
                  this.$refs.InputChat.onComplete();
                  if (this.askQuestionState === "2") this.refreshLeftList();
                  lastChat.helps = helps;
                  this.$forceUpdate();
                  this.scrollToBottom();
                },
              });
            });
          } else {
            // 处理 SQL、Cypher 查询的逻辑同上
            if (newStr.toLowerCase().startsWith("select")) {
              const { code, msg } = await serviceKnowledge.graphdataBaseSQl({
                sql: newStr,
              });
              if (code === 200) {
                await this.$nextTick(() => {
                  lastChat.done = true;
                  let str = parseMarkdown("答复: 根据提供的信息," + msg);
                  // newStr += "\n" + str;
                  // 追加文本
                  lastChat.appentText = str;
                  const ids = `#text-${this.chatList.length - 1}`;
                  this.typed = new Typed(ids, {
                    // 带查询语句
                    // strings: [newStr],
                    // 不带查询语句
                    strings: [str],
                    typeSpeed: 15,
                    loop: false,
                    showCursor: false,
                    onComplete: async () => {
                      this.pending = true;
                      const { code: updateCode } =
                        await serviceKnowledge.updateKnowledgeChat({
                          id,
                          format_result: str,
                        });
                      if (updateCode === 200) {
                        this.$refs.InputChat.onComplete();
                        if (this.askQuestionState === "2") {
                          this.refreshLeftList();
                        }
                      }
                      lastChat.helps = helps;
                      this.$forceUpdate();
                      this.scrollToBottom();
                    },
                  });
                });
              }
            } else {
              // await this.$nextTick(() => {
              //   lastChat.done = true;
              //   const ids = `#text-${this.chatList.length - 1}`;
              //   this.typed = new Typed(ids, {
              //     strings: [newStr],
              //     typeSpeed: 15,
              //     loop: false,
              //     showCursor: false,
              //     onComplete: () => {
              this.executeCypher(newStr);
              lastChat.helps = helps;
              this.$forceUpdate();
              this.scrollToBottom();
              //     },
              //   });
              // });
            }
          }
        }
      });
    },
    //  图数据库答案
    graphAnswer(val) {
      let { id, answer, done, docs, isStop, helps } = val;
      this.chatList.at(-1).id = id;
      this.chatList.at(-1).done = done;
      this.chatList.at(-1).docs = docs;
      this.chatList.at(-1).isStop = isStop;
      this.chatList.at(-1).SQL = "";
      this.chatList.at(-1).SQLShow = false;
      this.chatList.at(-1).good = false;
      // 追加文本
      this.chatList.at(-1).appentText = answer;
      // 删除 <p> 和 </p> 标签
      if (answer.includes("<p>") || answer.includes("</p>")) {
        answer = answer.replace(/<\/?p>/g, "");
      }
      this.$nextTick(() => {
        if (this.isHistory) {
          this.chatList.at(-1).html = answer;
          this.$forceUpdate();
          this.executeCypher(answer);
        } else {
          const id = "#text-" + JSON.stringify(this.chatList.length - 1);
          this.typed = new Typed(id, {
            strings: [answer], //输出的文字
            typeSpeed: 15, //打字的速度
            loop: false,
            showCursor: false,
            onComplete: () => {
              console.log("输入完成");
              this.chatList.at(-1).helps = helps;
              this.$forceUpdate();
              this.scrollToBottom();
              this.executeCypher(answer);
              this.$refs.InputChat.onComplete();
              // 刷新左侧历史记录
              this.askQuestionState === "2" && this.refreshLeftList();
            },
          });
        }
      });
    },
    // 数据库答案
    databaseAnswer(val) {
      const {
        id,
        answer,
        done,
        isStop,
        isflag,
        should_generate_chart,
        fig,
        df,
      } = val;
      this.chatList.at(-1).id = id;
      this.chatList.at(-1).done = done;
      this.chatList.at(-1).isflag = isflag;
      this.chatList.at(-1).should_generate_chart = should_generate_chart;
      this.chatList.at(-1).isStop = isStop;
      this.chatList.at(-1).fig = fig;
      this.chatList.at(-1).df = df;
      this.chatList.at(-1).good = false;
      // 追加文本
      this.chatList.at(-1).appentText = answer;
      console.log(isSqlStatement(answer));
      this.$nextTick(() => {
        if (this.isHistory) {
          this.chatList.at(-1).html = answer;
          this.$forceUpdate();
        } else {
          const id = "#text-" + JSON.stringify(this.chatList.length - 1);
          this.typed = new Typed(id, {
            strings: [answer], //输出的文字
            typeSpeed: 15, //打字的速度
            loop: false,
            showCursor: false,
            onComplete: () => {
              console.log("输入完成");
              this.$refs.InputChat.onComplete();
              // 刷新左侧历史记录
              this.askQuestionState === "2" && this.refreshLeftList();
              if (isflag) {
                const id = "#table-" + JSON.stringify(this.chatList.length - 1);
                this.typed = new Typed(id, {
                  strings: [df], //输出的文字
                  typeSpeed: 15, //打字的速度
                  loop: false,
                  showCursor: false,
                  onComplete: () => {
                    console.log("表格绘制完成");
                    if (should_generate_chart) {
                      this.$nextTick(() => {
                        this.$refs[
                          "Plotly-" + JSON.stringify(this.chatList.length - 1)
                        ][0].draw({
                          id:
                            "myPlot-" +
                            JSON.stringify(this.chatList.length - 1),
                          isShow: true,
                          data: fig.data,
                          layout: fig.layout,
                        });
                      });
                    }
                  },
                });
              }
            },
          });
        }
      });
    },
    textAnswer(val) {
      console.log(val);
      let { id, answer, done, result, docs, isStop, helps } = val;
      const istext = ["质量体系", "管理规程"].includes(this.knowledgeName);
      // 只替换换行符、制表符等，但保留单词之间的空格
      // if (istext) {
      answer = answer.replace(/[\n\t\r\f\v]+/g, "");
      // }

      // 使用正则表达式找到所有由《》包裹的文件名
      const filePattern = /《(.*?)》/g;
      let processedAnswer = answer;
      let fileNames = [];

      let match;
      while ((match = filePattern.exec(answer)) !== null) {
        fileNames.push(match[1]); // 捕获组1是文件名
        // 用占位符替换文件名，以便后续处理
        // const placeholder = `{{ }}`;
        const placeholder = `<span style="color:'#409eff'" class='FILE_${
          fileNames.length - 1
        }' > 《 ${match[1]} 》 </span>`;
        processedAnswer = processedAnswer.replace(match[0], placeholder);
      }
      // 更新 chatList 的最后一项
      this.chatList.at(-1).id = id;
      this.chatList.at(-1).done = done;
      this.chatList.at(-1).result = result;
      this.chatList.at(-1).docs = docs;
      this.chatList.at(-1).isStop = isStop;
      this.chatList.at(-1).good = false;
      // 追加文本
      this.chatList.at(-1).appentText = processedAnswer;

      if (this.isHistory) {
        // 如果是历史记录，直接设置 html
        this.chatList.at(-1).html = processedAnswer;
        this.$nextTick(() => {
          if (this.knowledgeName == "质量体系") {
            this.processFileNames(fileNames); // 处理文件名
          }
        });
      } else {
        this.$nextTick(() => {
          const id = "#text-" + JSON.stringify(this.chatList.length - 1);
          this.typed = new Typed(id, {
            strings: [processedAnswer], // 使用处理后的答案
            typeSpeed: 15,
            loop: false,
            showCursor: false,
            onComplete: () => {
              console.log("输入完成");
              this.chatList.at(-1).helps = helps;
              this.$forceUpdate();
              this.scrollToBottom();
              this.$refs.InputChat.onComplete();
              // 刷新左侧历史记录（如果条件满足）
              if (this.askQuestionState === "2") {
                this.refreshLeftList();
              }
              // 在动画完成后处理文件名
              if (this.knowledgeName == "质量体系") {
                this.processFileNames(fileNames); // 处理文件名
              }
            },
          });
        });
      }
    },

    processFileNames(fileNames) {
      // 遍历文件名列表，为每个文件名元素添加类名和点击事件
      fileNames.forEach((fileName, index) => {
        const selector = `#text-${this.chatList.length - 1} .FILE_${index}`;
        const element = this.$el.querySelector(selector);
        if (element) {
          // 替换占位符为实际文件名，并添加类名和点击事件
          // element.textContent = `《${fileName}》`;
          element.classList.add("fileName-link");
          element.addEventListener("click", async () => {
            // 在这里添加点击事件的逻辑，比如打开文件或显示文件详情
            console.log("Clicked on file:", fileName);
            // 请求文件地址
            const { code, msg } = await serviceKnowledge.getFileUrlByName({
              fileName,
            });
            if (code === 200) {
              if (msg == "知识库中暂无此文件！") {
                this.$message.error("知识库中暂无此文件！");
              } else {
                window.open(
                  this.kkFileView +
                    "/onlinePreview?url=" +
                    encodeURIComponent(Base64.encode(msg))
                );
              }
            }
          });
        }
      });
    },
    //  自动滚动
    scrollToBottom() {
      this.$nextTick(() => {
        // 获取要监听的 div 元素
        const resizeTarget = this.$refs.resizeTarget.at(-1);
        // 创建一个变量来跟踪用户是否正在滚动
        let isUserScrollingUp = false;
        // 监听鼠标滚轮事件
        const handleScroll = (event) => {
          // 判断滚轮方向
          // event.deltaY > 0 表示向下滚动，event.deltaY < 0 表示向上滚动
          isUserScrollingUp = event.deltaY < 0;
        };

        // 为 window 或特定的滚动容器添加滚轮事件监听器
        window.addEventListener("wheel", handleScroll, { passive: true });
        // 创建 ResizeObserver 实例
        const resizeObserver = new ResizeObserver((entries) => {
          // 如果用户正在向上滚动，则不执行自动滚动
          if (!isUserScrollingUp) {
            for (const entry of entries) {
              // 动态刷新滚动条
              let scrollElem = this.$refs.chatContainer;
              // console.log("开始滚动", scrollElem.scrollHeight);
              scrollElem.scrollTo({
                top: scrollElem.scrollHeight,
                behavior: "smooth",
              });
            }
          }
        });

        // 开始监听目标元素的大小变化
        resizeObserver.observe(resizeTarget);

        // 在组件销毁时停止监听，防止内存泄漏
        this.$once("hook:beforeDestroy", () => {
          console.log("销毁");
          resizeObserver.disconnect();
          window.removeEventListener("wheel", handleScroll);
        });
      });
    },
    // 复制
    copyFun() {
      console.log(`复制`);
      let clipboard = new ClipboardJS(".copy");
      clipboard.on("success", function (e) {
        console.log("复制成功!", e);
      });
    },
    // 停止回答
    stop(index) {
      this.typed && this.typed.stop();
      this.chatList[index].isStop = false;
      this.$refs.InputChat.onComplete();
      if (this.instanceTypeIt) {
        this.instanceTypeIt.freeze();
        this.eventSource.close();
        this.hideCursor();
        this.instanceTypeIt = null;
      }
      // this.$refs.InputChat.clearAiCheckQueryParams();
    },
    //删除回答
    delAns(index) {
      this.chatList.splice(index, 1);
      this.$refs.InputChat.onComplete();
    },
    // 给回答点赞
    async handleGood(val) {
      const { index, id } = val;
      this.chatList[index].good = !this.chatList[index].good;
      this.$forceUpdate();
      const { code } = await serviceKnowledge.giveThumbsUp({
        likeValue: this.chatList[index].good ? 1 : 0,
        id,
      });
      if (code === 200) {
        this.askQuestionState === "2" && this.refreshLeftList();
      }
    },
    // 文件生成
    async fileGeneration(index) {
      const { fileName } = this.chatList[index];
      const { code, data } = await serviceKnowledge.generateFile({
        fId: this.streamFId,
        fileName,
      });
      if (code === 200) {
        const params = {
          id: data,
          userId: this.$store.state.user.userId,
        };
        userService.downloadSourceFile(params).then((res) => {
          let blob = new Blob([res]);
          blob.text().then((result) => {
            try {
              let res = JSON.parse(result);
              if (res.code === 500) {
                this.$message.error(res.msg);
                return;
              }
            } catch (error) {
              console.log(blob);
              let objectUrl = URL.createObjectURL(blob);
              let link = document.createElement("a");
              // 源文件下载
              link.download = fileName + ".docx";
              // }
              link.href = objectUrl;
              this.$refs.InputChat.clearAiCheckQueryParams();
              link.click();
              link.remove();
            }
          });
        });
      }
      console.log("文件生成");
    },
    // 重新生成
    regenerate() {
      if (this.eventSource) {
        this.eventSource.close();
        this.loadAsk({ done: false, prompt: this.streamQuestion });
        this.$nextTick(() => {
          this.managementProceduresAnswer({
            message: "",
            query: this.streamQuestion,
            action: "0",
            answer: "",
          });
        });
      }
    },
    // 清除记录
    clearRecords() {
      this.$refs.InputChat.clearAiCheckQueryParams();
      this.streamFId = null;
    },
    // 向富文本追加文字
    append(item) {
      this.$emit("append", { ...item, fid: this.streamFId });
    },
    // 刷新左侧历史列表
    refreshLeftList() {
      console.log("更新左侧列表数据");
      this.$emit("updataAskList");
    },
    // 格式转换
    formattedTableData(tabledata) {
      console.log(tabledata);

      let newData = [];
      if (this.knowledgeName === "图数据库对话") {
        newData = tabledata.map((v, index) => ({
          index: index + 1,
          content: `${index + 1}:${v};`,
        }));
      } else {
        newData = tabledata.map((v, index) => ({
          index: index + 1,
          content: `${index + 1}:${v.doc_name};`,
          ...v,
        }));
      }
      console.log(newData);

      return newData;
    },
    // 文件跳转
    handleHref(docId, docName, index) {
      this.$refs.myPopover[index].doClose();
      this.$emit("handleMinimize");
      setTimeout(() => {
        this.$router.push({
          path: "/techmanage/filePreview",
          query: {
            id: docId,
            docName: docName,
          },
        });
      }, 500);
    },
    // 文件下载
    handleDownload(docId, docName) {
      getDocIdDetail(docId).then((resData) => {
        let name = resData.data.docName;
        userService
          .downloadSourceFile({
            id: docId,
            userId: this.$store.state.user.userId,
          })
          .then((res) => {
            let blob = new Blob([res]);
            blob.text().then((result) => {
              try {
                let res = JSON.parse(result);
                if (res.code === 500) {
                  this.$message.error(res.msg);
                  return;
                }
              } catch (error) {
                let objectUrl = URL.createObjectURL(blob);
                let link = document.createElement("a");
                // 源文件下载
                link.download = name;
                link.href = objectUrl;
                link.click();
                link.remove();
                this.visible = false;
              }
            });
          });
      });
    },
    // 文件预览
    handlePreview(docId, index) {
      this.$refs.myPopover[index].doClose();
      this.$emit("handleMinimize");
      this.$router.push({
        path: "/techmanage/assistedWriting/tem-file?id=" + docId,
      });
      // this.$store.commit("common/PDFDOCID", docId);
    },
    // 获取dom文本
    getTextFromElement(element) {
      let text = "";
      Array.from(element.childNodes).forEach((node) => {
        if (node.nodeType === Node.TEXT_NODE) {
          text += node.textContent.trim(); // 添加文本节点的内容，并去除首尾空格
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          text += this.getTextFromElement(node); // 递归处理元素节点
        }
      });
      return text;
    },
  },
  destroyed() {
    // this.$refs.myPopover.doClose();
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/chatCommon.scss";
.chatMain {
  flex: 1;
  padding: 20px;
  background-color: #f7f8fb;
  display: flex;
  flex-direction: column;
  // overflow-x: hidden;
  .cardList {
    width: 100%;
    flex: 1;
    overflow: auto;
    max-height: calc(100% - 100px);
	min-height: 300px;
    margin-bottom: 10px;
    .box-card {
      min-height: 150px;
      margin: 20px 40px 20px 40px;
	  width: calc(100% - 80px);
      .question {
        display: flex;
        width: 100%;
        border-bottom: 1px solid #d6cccc;
        padding-bottom: 10px;
        margin-bottom: 10px;
        img {
          width: 30px;
        }
        asks-Items {
          font-size: 16px;
          height: 30px;
          line-height: 30px;
          margin-left: 10px;
          width: 100%;
          min-height: 28px;
          box-sizing: border-box;
          white-space: pre-wrap;
          flex: 1;
          // margin: 0 10px;
          // position: relative;
          // overflow-x: scroll;
        }
      }
      .answer {
        display: flex;
        width: 100%;
        .imgbg {
          width: 30px;
          height: 30px;
          background: #10a37f;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          img {
            width: 25px;
          }
        }
        .done {
          width: 100%;
          display: flex;
          align-items: center;
        }
        .text {
          margin-left: 10px;
          white-space: pre-line;
        }
        .result {
          margin-top: 20px;
        }
        .result-item {
          margin-right: 10px;
        }
      }
    }
  }
}

// p {
//   margin: 0 !important;
// }

.key_input {
  margin-right: 20px;
  border: 1px solid #10a37f;
  padding: 10px 20px;
  border-radius: 8px;
}

.now_key {
  margin-top: 20px;
  background-color: #f3fdfb;
  border: 1px solid #10a37f;
  padding: 10px 20px;
  font-size: 16px;
  color: #10a37f;
  border-radius: 8px;
}

.qr_box {
  padding: 10px;
  margin: 20px auto;
  display: flex;
  justify-content: center;
  align-items: center;

  img {
    width: 240px;
    margin: 0 10px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #10a37f;
    border-radius: 8px;
  }
}

.show_qr {
  font-weight: bold;
  cursor: pointer;
  color: #10a37f;
}

@media screen and (max-width: 700px) {
  .qr_box {
    img {
      width: 150px;
    }
  }
}
::v-deep .v-spinner {
  padding-top: 5px;
  margin-left: 5px;
}
.typing {
  width: 100%;
  height: auto;
  // max-height: 300px;
  // overflow: auto;
}
/* 打字机 */
.typewriter {
  width: 1000px;
  margin: auto;
  background-color: #eeeeee;
  box-sizing: border-box;
  padding: 100px 80px;
  height: 100%;
  position: relative;
  overflow: hidden;
}
.ask-docs {
  width: 100%;
  min-width: 600px;
}
::v-deep .el-loading-mask {
  // background-color: rgb(231 239 255 / 38%)
  background-color: rgba(231, 239, 255, 0.38) !important;
}
.files {
  height: 65px;
  max-width: 800px;
  width: 100%;
  pointer-events: auto;
  border-radius: 8px;
  padding: 5px;
  overflow: auto;
  margin-bottom: 10px;
  display: grid;
  // grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  justify-items: center;
  overflow-x: hidden;
  .filesItem {
    display: flex;
    align-items: center;
    .text {
      width: 400px; /* 设置容器宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 文本溢出容器时隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    }
    img {
      width: 20px;
    }
  }
}

.tools {
  display: flex;
  justify-content: space-between;
}
.thumbs-up {
  display: flex;
  align-items: center;
}
.tips {
  display: flex;
  flex-direction: column;
  margin: 0 40px;
  .tag-item {
    width: 400px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 10px;
    cursor: pointer;
  }
}

::v-deep .el-tag--plain {
  background-color: white;
  border-color: #fff !important;
  color: #5d5858 !important;
}
/* ul ol 样式 */
::v-deep ul,
ol {
  margin: 10px 0 10px 20px;
}
::v-deep p {
  margin: 0px !important;
}
.cust {
  width: 100%;
}
//  ::v-deep cust{
::v-deep table {
  width: 100%;
  border: 1px solid #ccc;
}
::v-deep table td,
table th {
  border-bottom: 1px solid #ccc !important;
  border-right: 1px solid #ccc !important;
  padding: 5px 10px !important;
}
::v-deep table th {
  // border-bottom: 2px solid #ccc;
  text-align: center;
  background: #dee8ee;
}
::v-deep table tr {
  // border-bottom: 2px solid #ccc;
  text-align: center;
}
::v-deep table th:last-child {
  border-right: none;
}
::v-deep table td:last-child {
  border-right: none;
}

::v-deep table tr:last-child td {
  border-bottom: none;
}
::v-deep tr:nth-child(even) {
  background: #eff3f5;
}
.file-operation-item {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
}

::v-deep .fileName-link {
  cursor: pointer;
  color: #409eff;
}
::v-deep ul {
  padding-left: 0px !important;
}
</style>

<style lang="scss">
// .popper-file-operation .el-popover {
//     min-width: 0px !important;
//         background: aliceblue !important;
// }
// .popper-file-operation[x-placement^='bottom'] .popper__arrow {
//     border-bottom-color: #f56e48 !important;
// }

// .popper-file-operation[x-placement^='bottom'] .popper__arrow::after {
//     border-bottom-color: #f56e48 !important;
// }
.popper-file-operation.el-popover {
  // color: white;
  max-width: 400px;
  background: aliceblue !important;
  border-color: aliceblue !important;
  min-width: 0px !important;
  cursor: pointer;
}
.doc-item {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }

  .doc-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .doc-index {
      margin-right: 8px;
    }
  }

  .doc-content {
    margin-top: 10px;
    margin-left: 20px;
  }
}

::v-deep code {
  white-space: pre-wrap; /* CSS3 */
  word-wrap: break-word; /* IE6-7 */
}
</style>
