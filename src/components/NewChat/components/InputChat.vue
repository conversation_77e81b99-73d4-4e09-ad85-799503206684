<template>
  <div class="input-area">
    <div class="files" v-if="$store.state.common.files.length">
      <span
        class="filesItem"
        v-for="(item, index) in $store.state.common.files"
        :key="index"
      >
        <img src="@/assets/docCenter/file.png" alt="" />
        <span class="text"> {{ item }} </span>
      </span>
    </div>
    <div class="chat-footer-bar">
      <div class="chat-left">
        <textarea
          v-model.trim="prompt"
          placeholder="请输入您的问题"
          rows="1"
          @keydown.enter.prevent="ask"
        >
        </textarea>
      </div>
      <div class="chat-right action-buttons">
        <div class="voice-input-wrapper" :class="{ disabled: !pending }">
          <voice-input-button
            v-if="pending"
            v-model="result"
            @record="showResult"
            @record-start="recordStart"
            @record-stop="recordStop"
            @record-blank="recordNoResult"
            @record-failed="recordFailed"
            @record-ready="recordReady"
            @record-complete="recordComplete"
            interactiveMode="touch"
            color="transparent"
            tipPosition="top"
            class="custom-voice-btn"
          />
          <button
            class="mic-btn"
            :class="{ recording: isVoiceRecording, disabled: !pending }"
            :disabled="!pending"
            title="语音输入"
          >
            <img
              src="@/components/chat-v3/icons/microphone.svg"
              alt="Microphone"
              width="14"
              height="18"
            />
          </button>
        </div>

        <button
          v-if="pending"
          class="send-btn"
          @click="ask"
          :title="!prompt ? '请输入内容' : '发送消息'"
          :disabled="!prompt || isDisabled"
        >
          <img
            src="@/components/chat-v3/icons/send.svg"
            alt="Send"
            width="20"
            height="19"
          />
        </button>
        <RingLoader :loading="!pending" size="33px" />
      </div>
    </div>
  </div>
</template>

<script>
import RingLoader from "vue-spinner/src/RingLoader.vue";
import {
  sendQuestion,
  databaseSendQuestion,
  commonDatabaseSendQuestion,
  getChatSetting,
} from "./commonReq.js";
import {
  processDocs,
  firstLetterToUpperCase,
  parseMarkdown,
  executeCypher,
} from "../utils";
import { markdownTable } from "markdown-table";
import serviceKnowledge from "@/api/knowledge.js";
import { isSqlStatement } from "@/utils/coalmine.js";
export default {
  name: "",
  components: { RingLoader },
  props: {
    chatSet: {
      typeof: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 提问问题
      prompt: "",
      // 等待状态
      pending: true,
      //   语音值
      result: "",
      //   上次问答历史
      historyList: [
        // {
        //   role: "user",
        //   content: "",
        // },
        // {
        //   role: "assistant",
        //   content: "",
        // },
      ],
      newChatSet: "",
      knowledgeName: "",
      // 禁用是否发送
      isDisabled: false,
      // 原始问题
      orginPrompt: "",
      // 问题类型
      problemType: "answer",
      // 原始问题禁用字符串
      prependPrompt: "",
      // 问题校验参数
      aiCheckQuery: {
        // 常规是answer。流式是stream
        type: null,
        // 文件名
        fileName: null,
        // 0代表清空历史和文件名
        action: null,
        //查询语句，正常nuLl，如果有值就是改写之后的query
        query: null,
        //提示消息
        message: null,
        // 历史问答
        history: [],
      },
      isVoiceRecording: false,
    };
  },
  computed: {},
  watch: {
    chatSet: {
      handler(val) {
        if (val) {
          console.log(val);
          this.newChatSet = JSON.parse(JSON.stringify(val));
          this.clearAiCheckQueryParams();
          this.knowledgeName = this.newChatSet.knowledge_name;
          this.prompt = "";
          this.pending = true;
        }
      },
      deep: true,
    },
  },
  created() {},
  mounted() {},
  methods: {
    clearAiCheckQueryParams() {
      this.aiCheckQuery = {
        // 常规是answer。流式是stream
        type: null,
        // 文件名
        fileName: null,
        // 0代表清空历史和文件名
        action: null,
        //查询语句，正常nuLl，如果有值就是改写之后的query
        query: "",
        //提示消息
        message: null,
        // 历史问答
        history: [],
      };
    },

    askQuestion(prompt, chatSet) {
      this.prompt = prompt;
      this.newChatSet = chatSet;

      this.ask();
    },
    ask() {
      // 如果按钮被禁用，则不执行任何操作
      if (this.isDisabled) {
        // this.$message.warning("正在解析问题中....");
        return;
      }
      // 检查是否有输入内容
      if (!this.prompt) {
        this.$message.warning("请输入提问内容");
        return;
      }
      this.orginPrompt = JSON.parse(JSON.stringify(this.prompt));
      // 设置 pending 状态为 true，表示正在处理请求
      this.pending = true;
      this.isDisabled = true;

      this.$emit("loadAsk", { done: false, prompt: this.prompt });
      // 重置 pending 状态
      this.pending = false;
      const istext = [
        "数据库对话",
        "图数据库对话",
        "医药图谱",
        "医药法规",
        "计划经营",
        "管理规程",
      ].includes(this.knowledgeName);
      if (istext) {
        switch (this.knowledgeName) {
          case "数据库对话":
            this.databaseSendQuestionFn();
            break;
          case "图数据库对话":
            this.graphSendQuestionFn();
            break;
          case "医药图谱":
            this.medicalAtlasSendQuestionFn();
            break;
          case "医药法规":
            this.medicalRegulationsFn();
            break;
          case "计划经营":
            this.plannedOperationFn();
            break;
          case "管理规程":
            this.managementProceduresFn();
            break;
          default:
            break;
        }
      } else {
        this.textSendQuestion(); // 调用文本问答方法
      }
    },
    //  调用管理规程问答方法
    async managementProceduresFn() {
      let { type, fileName, action, query, message, history } =
        this.aiCheckQuery;
      let a = 1;

      if (history.length == 0) {
        this.aiCheckQuery.history.push({
          role: "user",
          content: this.prompt,
        });
        console.log("存储第一个历史记录", this.aiCheckQuery.history);
      }
      try {
        const { code, data } =
          await serviceKnowledge.useEventSourceOrAioxsByQuery({
            // 常规是answer。流式是stream
            type,
            // 文件名
            fileName,
            // 0代表清空历史和文件名
            action,
            //查询语句，正常nuLl，如果有值就是改写之后的query
            query: this.orginPrompt,
            //提示消息
            message,
            //历史问答
            history,
            // 提示词
            promptName: this.newChatSet.prompt_name,
            // 知识名称
            knowledgeName: this.knowledgeName,
          });
        if (code !== 200) {
          this.pending = false;
          this.$emit("resetAsk");
          return this.$message.error("请重新提问");
        }
        this.aiCheckQuery.message = data.message;
        this.aiCheckQuery.query = data.query;
        this.aiCheckQuery.action = data.action;
        this.aiCheckQuery.fileName = data.fileName;
        this.aiCheckQuery.type = data.type;

        const { message, query, action } = this.aiCheckQuery;
        this.problemType = data.type;
        switch (data.type) {
          case "stream":
            this.aiCheckQuery.history = data.history;
            this.$emit("getAnswer", { message, query, action });
            break;
          default:
            this.textSendQuestion(); // 调用默认文本问答方法
            break;
        }
      } catch (error) {
        console.error("Error fetching problem type:", error);
        this.pending = false;
        this.$emit("resetAsk");
        this.$message.error("无法获取问题类型，请稍后重试");
      }
    },
    // 调用计划经营数据库问答方法
    async plannedOperationFn() {
      const { code, data } = await sendQuestion(
        this.prompt,
        this.newChatSet,
        this.historyList
      );

      if (code == 200) {
        let { answer } = data;
        const isSql = isSqlStatement(answer);
        if (isSql) {
          // 1.保存问答上次历史
          this.historyList = [
            {
              role: "user",
              content: this.prompt,
            },
            {
              role: "assistant",
              content: answer,
            },
          ];
          const res = await commonDatabaseSendQuestion(
            this.prompt,
            answer,
            this.knowledgeName
          );

          if (res.text) {
            const { df, fig, should_generate_chart, isflag, id } = res;

            let isSingleRow = "";
            if (df?.length && df[0][0] == "content") {
              isSingleRow = parseMarkdown(df[1][0]);
            }
            await serviceKnowledge.updateKnowledgeChat({
              id: data.id,
              // format_result: isSingleRow? JSON.stringify(df[1][0]) :JSON.stringify({table:df,draw:{should_generate_chart,fig}}),
              format_result: isSingleRow
                ? JSON.stringify(df[1][0])
                : JSON.stringify(df),
            });
            console.log(df);
            this.$emit("getAnswer", {
              id,
              answer: parseMarkdown(res.text),
              done: true,
              isStop: true,
              isflag,
              should_generate_chart,
              fig,
              df: df && df.length && parseMarkdown(markdownTable(df)),
              isSingleRow,
              isSql,
            });
            this.prompt = "";
          } else {
            this.$emit("resetAsk");
            this.pending = true;
            return this.$message.error("请重新提问");
          }
        } else {
          if (data.answer) {
            let { answer, docs, id, result, router, helps } = data;
            // 1.保存问答上次历史
            this.historyList = [
              {
                role: "user",
                content: this.prompt,
              },
              {
                role: "assistant",
                content: answer,
              },
            ];
            // 2. 过滤文档来源数据

            if (docs && docs.length) {
              docs = processDocs(docs);
            }
            // 3.生成文件地址
            result = result
              ? process.env.VUE_APP_BASE_GATE_WAY_URL + "/" + result
              : "";
            console.log(parseMarkdown(answer));
            // 4.回答答案回调 并清空问题 刷新左侧历史数据
            this.$emit("getAnswer", {
              id,
              answer: parseMarkdown(answer),
              done: true,
              docs,
              result,
              isStop: true,
              helps,
              isSql,
            });
            this.prompt = "";
            // 6.如果 router存在  则跳转对应功能
            if (router) {
              let routerUrl = firstLetterToUpperCase(router);
              //   缩小当前弹框
              this.$store.commit("common/isMinimize", true);
              this.$router.push({
                name: routerUrl,
              });
            }
          } else {
            this.$emit("resetAsk");
            this.pending = true;
            return this.$message.error("请重新提问");
          }
        }
      }
    },
    // 调用医药法规数据库问答方法
    async medicalRegulationsFn() {
      const knowledge_name = "医药法规数据库";
      // 调用医药图谱的方法
      // 获取医药图谱配置
      const res = await getChatSetting(knowledge_name);
      if (res.code === 200) {
        this.newChatSet = { ...res.data[0], knowledge_name };
        const { code, data } = await sendQuestion(
          this.prompt,
          this.newChatSet,
          this.historyList
        );
        if (code === 200 && data.answer) {
          let { answer, docs, id, helps, docResult } = data;
          // 1.保存问答上次历史
          this.historyList = [
            {
              role: "user",
              content: this.prompt,
            },
            {
              role: "assistant",
              content: answer,
            },
          ];
          // 2. 过滤文档来源数据
          if (docs && docs.length) {
            docs = processDocs(docs);
          }
          // 3. 连接neo4j数据库获取数据
          // const executeCypherObj= await executeCypher(answer)
          // 4.回答答案回调 并清空问题 刷新左侧历史数据
          this.$emit("getAnswer", {
            id,
            answer,
            done: false,
            docs,
            isStop: true,
            helps,
            prompt: this.prompt,
            docResult,
          });
          this.prompt = "";
        } else {
          this.$emit("resetAsk");
          this.pending = true;
          return this.$message.error("请重新提问");
        }
      }
    },
    // 调用医药图谱问答方法
    async medicalAtlasSendQuestionFn() {
      const { code, data } = await sendQuestion(
        this.prompt,
        this.newChatSet,
        this.historyList
      );
      if (code === 200 && data.answer) {
        let { answer, docs, id, helps } = data;
        // 1.保存问答上次历史
        this.historyList = [
          {
            role: "user",
            content: this.prompt,
          },
          {
            role: "assistant",
            content: answer,
          },
        ];
        // 2. 过滤文档来源数据
        if (docs && docs.length) {
          docs = processDocs(docs);
        }
        // 3. 连接neo4j数据库获取数据
        // const executeCypherObj= await executeCypher(answer)
        // 4.回答答案回调 并清空问题 刷新左侧历史数据
        this.$emit("getAnswer", {
          id,
          answer,
          done: true,
          docs,
          isStop: true,
          helps,
          prompt: this.prompt,
        });
        this.prompt = "";
      } else {
        this.$emit("resetAsk");
        this.pending = true;
        return this.$message.error("请重新提问");
      }
    },
    //  调用图数据库问答方法
    async graphSendQuestionFn() {
      const { code, data } = await sendQuestion(
        this.prompt,
        this.newChatSet,
        this.historyList
      );
      if (code === 200 && data.answer) {
        let { answer, docs, id, helps } = data;
        // 1.保存问答上次历史
        this.historyList = [
          {
            role: "user",
            content: this.prompt,
          },
          {
            role: "assistant",
            content: answer,
          },
        ];
        // 2. 过滤文档来源数据
        if (docs && docs.length) {
          docs = processDocs(docs);
        }
        // 3. 连接neo4j数据库获取数据
        // const executeCypherObj= await executeCypher(answer)
        // 4.回答答案回调 并清空问题 刷新左侧历史数据
        this.$emit("getAnswer", {
          id,
          answer,
          done: true,
          docs,
          isStop: true,
          helps,
        });
        this.prompt = "";
      } else {
        this.$emit("resetAsk");
        this.pending = true;
        return this.$message.error("请重新提问");
      }
    },
    // 调用数据库问答方法
    async databaseSendQuestionFn() {
      const res = await databaseSendQuestion(this.prompt, this.knowledgeName);
      if (res.text) {
        const { df, fig, should_generate_chart, isflag, id } = res;
        this.$emit("getAnswer", {
          id,
          answer: parseMarkdown(res.text),
          done: true,
          isStop: true,
          isflag,
          should_generate_chart,
          fig,
          df: df && df.length && parseMarkdown(markdownTable(df)),
        });
        this.prompt = "";
      } else {
        this.$emit("resetAsk");
        this.pending = true;
        return this.$message.error("请重新提问");
      }
    },
    // 调用文本问搭方法 - 直接使用流式接口
    async textSendQuestion() {
      try {
        console.log('使用流式接口发送问题:', this.prompt);

        const query = this.prompt;

        // 添加用户消息
        this.$emit("loadAsk", { done: false, prompt: query });

        // 添加AI消息占位符并开始流式聊天
        this.$parent.chatList.push({
          prompt: '', // AI消息没有prompt
          done: false,
          answer: '', // 流式内容会填充到这里
          isStop: true,
          id: Date.now(),
          helps: [],
          docs: [],
          isRecords: false,
          isVoicePlay: false,
        });

        // 更新历史记录
        this.historyList = [
          {
            role: "user",
            content: query,
          },
          {
            role: "assistant",
            content: "", // 流式接口会逐步填充
          },
        ];

        // 直接调用父组件的流式方法
        await this.$parent.startStreamChat(query);

        // 清空输入框
        this.prompt = "";

      } catch (error) {
        console.error('流式请求失败:', error);
        this.$emit("resetAsk");
        this.pending = true;
        return this.$message.error("请求失败，请重新提问");
      }
    },

    // 保留原有的非流式方法作为备用
    async textSendQuestionOld() {
      console.log(this.newChatSet);
      const { code, data } = await sendQuestion(
        this.prompt,
        this.newChatSet,
        this.historyList
      );
      if (code === 200 && data.answer) {
        let { answer, docs, id, result, router, helps, docResult } = data;
        // 1.保存问答上次历史
        this.historyList = [
          {
            role: "user",
            content: this.prompt,
          },
          {
            role: "assistant",
            content: answer,
          },
        ];
        // 2. 过滤文档来源数据

        if (docs && docs.length) {
          docs = processDocs(docs);
          console.log(docs);
        }
        // 3.生成文件地址
        result = result
          ? process.env.VUE_APP_BASE_GATE_WAY_URL + "/" + result
          : "";
        // 4.回答答案回调 并清空问题 刷新左侧历史数据
        console.log(answer);
        this.$emit("getAnswer", {
          id,
          answer: parseMarkdown(answer),
          done: true,
          docs,
          result,
          isStop: true,
          helps,
          docResult,
        });
        this.prompt = "";
        // 6.如果 router存在  则跳转对应功能
        if (router) {
          let routerUrl = firstLetterToUpperCase(router);
          //   缩小当前弹框
          this.$store.commit("common/isMinimize", true);
          this.$router.push({
            name: routerUrl,
          });
        }
      } else {
        this.$emit("resetAsk");
        this.pending = true;
        return this.$message.error("请重新提问");
      }
    },
    // 流式请求
    async streamSendQuestion(message = "", query = "") {
      // this.$emit("streamAnswer", { message, query });
    },

    // 输入完成回调
    onComplete() {
      this.pending = true;
      this.prompt = "";
      this.isDisabled = false;
      console.log("onComplete===========>",this.isDisabled);
    },
    // 录音
    recordReady() {
      console.info("按钮就绪!");
    },
    recordStart() {
      this.isVoiceRecording = true;
      console.info("录音开始");
    },
    showResult(text) {
      console.info("收到识别结果：", text);
      this.prompt = text;
    },
    recordStop() {
      this.isVoiceRecording = false;
      console.info("录音结束");
    },
    recordNoResult() {
      console.info("没有录到什么，请重试");
    },
    recordComplete(text) {
      console.info("识别完成! 最终结果：", text);
    },
    recordFailed(error) {
      this.isVoiceRecording = false;
      console.info("识别失败，错误栈：", error);
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/assets/styles/chatCommon.scss";

.input-area {
  background: #fff;
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.12);
  flex-shrink: 0;
  margin-top: auto;
  position: relative;
  z-index: 10;

  textarea {
    width: 100%;
    border: none;
    outline: none;
    resize: none;
    font-size: 14px;
    height: 80px;
    background-color: transparent;
    margin-bottom: 10px;
  }
}

.chat-footer-bar {
  display: flex;
  align-items: center;
  gap: 10px;
  padding-top: 10px;
}
.chat-left {
  flex: 1;
  min-width: 0;
}
.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.send-btn,
.stop-btn,
.mic-btn {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  min-width: 40px;
  min-height: 40px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-btn {
  background: #145bff;

  &:hover:not(:disabled) {
    background: #0d47d9;
  }

  &:disabled {
    background: #d1d5db;
    cursor: not-allowed;
    opacity: 0.6;

    img {
      opacity: 0.5;
    }
  }
}

.voice-input-wrapper {
  position: relative;
  display: inline-block;

  &.disabled {
    pointer-events: none;
    opacity: 0.6;
  }
}

.mic-btn {
  background: #6b7280;
  position: relative;
  z-index: 1;

  img {
    filter: brightness(0) invert(1);
  }

  &:hover:not(.disabled) {
    background: #4b5563;
  }

  &.recording {
    background: #ef4444;
    animation: pulse 1.5s infinite;
  }

  &.disabled {
    background: #d1d5db;
    cursor: not-allowed;
    opacity: 0.6;

    &:hover {
      background: #d1d5db;
    }
  }
}

.custom-voice-btn {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 40px !important;
  height: 40px !important;
  opacity: 0 !important;
  z-index: 10 !important;
  border-radius: 50% !important;
  cursor: pointer !important;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}

.files {
  height: 65px;
  max-width: 800px;
  width: 100%;
  pointer-events: auto;
  border-radius: 8px;
  padding: 5px;
  overflow: auto;
  margin-bottom: 10px;
  display: grid;
  // grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  justify-items: left;
  overflow-x: hidden;
  .filesItem {
    display: flex;
    align-items: center;
    .text {
      max-width: 400px; /* 设置容器宽度 */
      white-space: nowrap; /* 防止文本换行 */
      overflow: hidden; /* 文本溢出容器时隐藏 */
      text-overflow: ellipsis; /* 使用省略号表示被隐藏的文本 */
    }
    img {
      width: 20px;
    }
  }
}
.isDisabled {
  border: none;
  position: absolute;
  right: 10%;
  height: 60px;
  &::placeholder {
    color: #909090; /* 将颜色改为灰色 */
  }
}
.custom-input {
  height: 60px !important;
}
::v-deep .custom-input .el-input__inner {
  height: 60px !important;
}
::v-deep .custom-input .el-input-group__prepend {
  color: #000 !important;
  border-radius: 8px;
}
</style>
