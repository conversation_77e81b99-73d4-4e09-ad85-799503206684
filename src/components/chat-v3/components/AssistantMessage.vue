<template>
  <div class="chat-message-container">
    <div class="chat-message">
      <div class="avatar avatar-bot">
        <!-- 使用当前知识库的图标 -->
        <img
          v-if="currentKnowledgeIcon"
          :src="currentKnowledgeIcon"
          alt="AI Assistant"
        />
      </div>
      <div class="message-content">
        <!-- 加载状态 - 只在没有任何内容时显示 -->
        <div v-if="!message.done && !message.answer" class="loading-content">
          <div class="loading-text">AI正在思考中...</div>
          <div class="loading-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>

        <!-- 消息内容 - 有内容就显示，不管是否完成 -->
        <div v-if="message.answer || message.done" class="message-body">
          <!-- 消息发送者（如果有的话） -->
          <div v-if="message.sender" class="message-sender">{{ message.sender }}</div>

          <!-- 思考内容 -->
          <div v-if="thinkContent" class="think-section">
            <el-collapse v-model="activeThinkPanel">
              <el-collapse-item name="think">
                <template slot="title">
                  <span class="think-title">
                    <span class="think-icon">🤔</span>
                    AI 思考过程
                  </span>
                </template>
                <div class="think-content">{{ thinkContent }}</div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 文本回答 -->
          <v-md-preview :text="normalContent"></v-md-preview>

          <!-- 流式输入指示器 -->
          <div v-if="message.answer && !message.done" class="streaming-indicator">
            <span class="cursor-blink">|</span>
          </div>

          <!-- 文档来源 -->
          <div v-if="processedDocs && processedDocs.length > 0" class="file-sources">
            <el-collapse v-model="activeDocsPanel" accordion>
              <el-collapse-item name="docs">
                <template slot="title">
                  <el-tag size="small" type="primary">文件来源</el-tag>
                </template>
                <div class="docs-list">
                  <div
                    v-for="(doc, index) in processedDocs"
                    :key="index"
                    class="doc-item"
                  >
                    <span class="doc-index">{{ index + 1 }}.</span>
                    <el-link
                      type="primary"
                      :underline="false"
                      @click="handleDocPreview(doc.link, doc.fileName)"
                      class="doc-link"
                    >
                      {{ doc.fileName }}
                    </el-link>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息操作工具栏 -->
    <MessageToolbar
      v-if="message.done"
      :message="message"
      @delete="$emit('delete', $event)"
      @like="$emit('like', $event)"
    />
  </div>
</template>

<script>
import MessageToolbar from './MessageToolbar.vue'
import { mapState } from 'vuex'
import { preview } from '@/utils/fileStreamPreview'

export default {
  name: 'AssistantMessage',
  components: {
    MessageToolbar
  },
  props: {
    // 消息对象
    message: {
      type: Object,
      required: true
    },
    // 消息索引
    messageIndex: {
      type: Number,
      required: true
    }
  },

  data() {
    return {
      activeThinkPanel: ['think'],
      activeDocsPanel: 'docs' // 默认展开文档来源
    }
  },

  computed: {
    ...mapState('chat-v3', ['currentKnowledge']),

    // 获取当前知识库的图标
    currentKnowledgeIcon() {
      return this.currentKnowledge?.image || null;
    },

    // 解析思考内容
    thinkContent() {
      if (!this.message.answer) return '';

      const text = this.message.answer;

      // 提取完整的think标签内容
      const thinkMatch = text.match(/<think>([\s\S]*?)<\/think>/);
      if (thinkMatch) {
        return thinkMatch[1].trim();
      }

      // 如果有未闭合的think标签且消息未完成
      if (!this.message.done && text.includes('<think>')) {
        const openThinkMatch = text.match(/<think>([\s\S]*)$/);
        if (openThinkMatch) {
          return openThinkMatch[1].trim();
        }
      }

      return '';
    },

    // 获取普通内容（移除think标签）
    normalContent() {
      if (!this.message.answer) return '';

      let content = this.message.answer;

      // 移除完整的think标签
      content = content.replace(/<think>[\s\S]*?<\/think>/g, '');

      // 如果消息未完成，移除未闭合的think标签
      if (!this.message.done) {
        content = content.replace(/<think>[\s\S]*$/, '');
      }

      return content.trim();
    },

    // 处理文档来源数据
    processedDocs() {
      if (!this.message.docs || !Array.isArray(this.message.docs)) {
        return [];
      }

      // 处理字符串数组，只分割前两个逗号
      const processedDocs = this.message.docs.map(doc => {
        const firstComma = doc.indexOf(',');
        const secondComma = doc.indexOf(',', firstComma + 1);

        const fileName = firstComma > -1 ? doc.substring(0, firstComma).trim() : doc;
        const link = secondComma > -1 ? doc.substring(firstComma + 1, secondComma).trim() : '';
        const content = secondComma > -1 ? doc.substring(secondComma + 1).trim() : '';

        return { fileName, link, content };
      });

      // 根据文件名去重，保留第一个出现的文档
      const uniqueDocs = [];
      const seenFileNames = new Set();

      for (const doc of processedDocs) {
        if (!seenFileNames.has(doc.fileName)) {
          seenFileNames.add(doc.fileName);
          uniqueDocs.push(doc);
        }
      }

      return uniqueDocs;
    }
  },

  watch: {
    'message.answer': {
      handler() {
        this.$nextTick(() => {
          this.triggerMermaidRender();
        });
      },
      immediate: false
    },
    'message.done': {
      handler(newVal) {
        if (newVal) {
          this.$nextTick(() => {
            this.triggerMermaidRender();
          });
        }
      },
      immediate: false
    },
    'message.docs': {
      handler() {
        // 强制重新渲染组件以更新文档来源显示
        this.$forceUpdate();
      },
      immediate: false,
      deep: true
    }
  },

  methods: {
    triggerMermaidRender() {
      if (typeof window === 'undefined' || !window.mermaid) {
        return
      }

      try {
        // 查找所有未渲染的 mermaid 元素
        const unprocessedElements = document.querySelectorAll('.mermaid:not([data-processed])')

        if (unprocessedElements.length > 0) {
          // 为每个元素添加唯一ID并渲染
          unprocessedElements.forEach((element, index) => {
            const uniqueId = `mermaid-${Date.now()}-${index}`
            element.id = uniqueId

            try {
              window.mermaid.init(undefined, element)
              element.setAttribute('data-processed', 'true')
            } catch (error) {
              console.warn('Mermaid render warning:', error)
              element.setAttribute('data-processed', 'true')
            }
          })
        }
      } catch (error) {
        console.error('Error in triggerMermaidRender:', error)
      }
    },

    // 处理文档预览
    async handleDocPreview(link, fileName) {
      try {
        if (!link) {
          this.$message.warning('文件链接不存在');
          return;
        }

        // 直接使用preview函数预览文件
        await preview(link, fileName);
      } catch (error) {
        console.error('文件预览失败:', error);
        this.$message.error('文件预览失败，请稍后重试');
      }
    }
  }
}
</script>

<style>
.github-markdown-body {
  padding: 0 !important;
}
</style>

<style lang="scss" scoped>
// 根据设计稿的CSS变量
:root {
  --bg-color: #F7F8FB;
  --sidebar-bg-color: #FFFFFF;
  --header-bg-color: #FFFFFF;
  --chat-bubble-user-bg: #FFFFFF;
  --chat-bubble-bot-bg: #FFFFFF;
  --input-area-bg: #FFFFFF;
  --border-color: #E8E8E8;
  --text-primary: #313233;
  --text-secondary: #949699;
  --text-link: #0676DB;
  --accent-color: #2D8CF0;
  --placeholder-text: #C6C8CC;
  --button-hover-bg: #F5F6F7;
}

.chat-message-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: #FFFFFF;
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  padding: 15px 20px;

  .chat-message {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      flex-shrink: 0;
      overflow: hidden;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
      }
    }

    .message-content {
      display: flex;
      flex-direction: column;
      gap: 12px;
      flex-grow: 1;
    }
  }
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 12px;

  .loading-text {
    color: #666;
    font-size: 14px;
  }

  .loading-dots {
    display: flex;
    gap: 4px;

    span {
      width: 6px;
      height: 6px;
      background: #409eff;
      border-radius: 50%;
      animation: loading-bounce 1.4s ease-in-out infinite both;

      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
}

.message-body {
  .message-sender {
    font-size: 14px;
    line-height: 1.5;
    color: #313233;
    margin-bottom: 12px;
  }

  .message-text {
    font-size: 14px;
    line-height: 24px;
    color: #313233;

    // Markdown样式
    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 8px 0;
      font-weight: 600;
    }

    p {
      margin: 8px 0;
    }

    code {
      background: #f5f5f5;
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Monaco', 'Consolas', monospace;
      font-size: 0.9em;
    }

    pre {
      background: #f8f8f8;
      border: 1px solid #e8e8e8;
      border-radius: 6px;
      padding: 12px;
      overflow-x: auto;
      margin: 12px 0;

      code {
        background: none;
        padding: 0;
      }
    }

    ul, ol {
      margin: 8px 0;
      padding-left: 24px;
    }

    blockquote {
      border-left: 4px solid #409eff;
      margin: 12px 0;
      padding: 8px 16px;
      background: #f8f9fa;
      color: #666;
    }
  }
}



.file-sources {
  padding-top: 12px;
  border-top: 1px solid #E8E8E8;
  margin-top: 16px;

  :deep(.el-collapse) {
    border: none;

    .el-collapse-item {
      border-bottom: none;

      .el-collapse-item__header {
        height: auto;
        line-height: 1.5;
        padding: 8px 0;
        border-bottom: none;
        background: transparent;
        font-size: 14px;

        &:hover {
          background: #f5f7fa;
        }
      }

      .el-collapse-item__content {
        padding: 12px 0 0 0;
        color: #8b8b8b;
      }
    }
  }

  .docs-list {
    .doc-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding: 6px 0;

      &:last-child {
        margin-bottom: 0;
      }

      .doc-index {
        color: #409eff;
        font-weight: 500;
        margin-right: 8px;
        min-width: 20px;
      }

      .doc-link {
        font-size: 14px;

        &:hover {
          color: #66b1ff;
        }
      }
    }
  }
}

// 流式输入指示器样式
.streaming-indicator {
  display: inline-block;
  margin-left: 2px;

  .cursor-blink {
    animation: blink 1s infinite;
    font-weight: bold;
    color: #666;
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// Mermaid 图表样式
:deep(.mermaid-container) {
  margin: 16px 0;
  text-align: center;
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;

  &.mermaid-rendered {
    background: transparent;
    border: none;
    padding: 0;
  }

  svg {
    max-width: 100%;
    height: auto;
  }

  .mermaid-error {
    color: #dc3545;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    padding: 12px;
    font-size: 14px;
  }
}

// Think标签样式
.think-section {
  margin: 0 0 16px 0;

  :deep(.el-collapse) {
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-collapse-item__header) {
    background: linear-gradient(135deg, #f1f3f4 0%, #e8eaed 100%);
    border-bottom: 1px solid #e1e5e9;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #5f6368;

    &:hover {
      background: linear-gradient(135deg, #e8eaed 0%, #dadce0 100%);
    }

    .think-title {
      display: flex;
      align-items: center;

      .think-icon {
        margin-right: 10px;
        font-size: 16px;
        filter: grayscale(0.3);
      }
    }
  }

  :deep(.el-collapse-item__content) {
    padding: 20px;
    background: #ffffff;
    border-bottom: none;
  }

  .think-content {
    color: #8b8b8b;
    font-size: 14px;
    line-height: 1.7;
    white-space: pre-wrap;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }
}

/* Mermaid 图表样式 */
.message-text .mermaid {
  text-align: center;
  margin: 16px 0;
  padding: 16px;
  background: #f9f9f9;
  border-radius: 8px;
  border: 1px solid #e1e4e8;
}

@keyframes loading-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
</style>
